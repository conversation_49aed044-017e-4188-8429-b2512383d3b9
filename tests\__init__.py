"""
Test Suite for Schwab Telegram Trading Bot
Comprehensive testing framework with unit tests, integration tests, and mocks.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import os
import sys
from pathlib import Path

# Add src directory to Python path for testing
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

# Test configuration
TEST_CONFIG = {
    'schwab': {
        'client_id': 'test_client_id',
        'client_secret': 'test_client_secret',
        'redirect_uri': 'http://localhost:8080/callback',
        'account_number': 'test_account'
    },
    'telegram': {
        'bot_token': 'test_bot_token',
        'chat_id': 'test_chat_id'
    },
    'trading': {
        'paper_trading': True,
        'max_positions': 5,
        'max_position_size': 0.02
    }
}

__all__ = ['TEST_CONFIG']
