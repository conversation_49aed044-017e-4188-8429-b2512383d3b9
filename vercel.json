{"version": 2, "name": "schwab-telegram-trading-bot", "description": "Professional algorithmic trading bot with Schwab API and Telegram integration", "alias": ["schwab-trading-bot", "trading-bot-schwab"], "build": {"env": {"PYTHON_VERSION": "3.11", "ENVIRONMENT": "production"}}, "buildCommand": "pip install -r requirements.txt && python -m pytest tests/ --tb=short", "functions": {"src/api/*.py": {"runtime": "python3.11", "maxDuration": 30, "memory": 512, "environment": {"PYTHONPATH": "src"}}}, "routes": [{"src": "/api/health", "dest": "/src/api/health.py", "methods": ["GET"]}, {"src": "/api/metrics", "dest": "/src/api/metrics.py", "methods": ["GET"]}, {"src": "/api/webhook/schwab", "dest": "/src/api/schwab_webhook.py", "methods": ["POST"]}, {"src": "/api/webhook/telegram", "dest": "/src/api/telegram_webhook.py", "methods": ["POST"]}, {"src": "/api/portfolio", "dest": "/src/api/portfolio.py", "methods": ["GET"]}, {"src": "/api/trades", "dest": "/src/api/trades.py", "methods": ["GET", "POST"]}, {"src": "/api/strategies", "dest": "/src/api/strategies.py", "methods": ["GET", "POST", "PUT"]}, {"src": "/dashboard/(.*)", "dest": "/dashboard/$1"}, {"src": "/(.*)", "dest": "/index.html"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://api.schwabapi.com https://api.telegram.org wss:"}]}], "env": {"ENVIRONMENT": "production", "PYTHONPATH": "src", "LOG_LEVEL": "INFO", "ENABLE_METRICS": "true"}, "regions": ["iad1", "sfo1", "lhr1"], "github": {"enabled": true, "autoAlias": true, "autoJobCancelation": true, "silent": false}, "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}, {"source": "/dashboard/(.*)", "destination": "/dashboard/index.html"}], "redirects": [{"source": "/docs", "destination": "https://github.com/HectorTa1989/schwab-telegram-trading-bot/blob/main/README.md", "permanent": true}, {"source": "/github", "destination": "https://github.com/HectorTa1989/schwab-telegram-trading-bot", "permanent": true}], "cleanUrls": true, "trailingSlash": false, "crons": [{"path": "/api/cron/market-open", "schedule": "30 9 * * 1-5"}, {"path": "/api/cron/market-close", "schedule": "0 16 * * 1-5"}, {"path": "/api/cron/daily-summary", "schedule": "0 17 * * 1-5"}, {"path": "/api/cron/health-check", "schedule": "*/5 * * * *"}], "installCommand": "pip install -r requirements.txt", "outputDirectory": "dist", "framework": null, "public": false}