{"schwab": {"client_id": "YOUR_SCHWAB_CLIENT_ID", "client_secret": "YOUR_SCHWAB_CLIENT_SECRET", "redirect_uri": "http://localhost:8080/callback", "account_number": "YOUR_ACCOUNT_NUMBER", "base_url": "https://api.schwabapi.com", "rate_limit": {"requests_per_minute": 120, "burst_limit": 10}}, "telegram": {"bot_token": "YOUR_TELEGRAM_BOT_TOKEN", "chat_id": "YOUR_TELEGRAM_CHAT_ID", "alert_frequency": "immediate", "notification_types": {"trades": true, "portfolio_updates": true, "risk_alerts": true, "system_status": true, "market_analysis": false}}, "trading": {"paper_trading": true, "max_positions": 10, "max_position_size": 0.05, "max_portfolio_risk": 0.02, "min_trade_amount": 100, "strategies_enabled": ["momentum_breakout", "earnings_momentum", "institutional_following"], "trading_hours": {"start": "09:30", "end": "16:00", "timezone": "US/Eastern", "extended_hours": false}, "market_data": {"update_frequency": 60, "symbols_watchlist": ["SPY", "QQQ", "IWM", "VIX", "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "META", "NFLX", "AMD", "CRM"]}}, "risk_management": {"stop_loss_pct": 0.02, "take_profit_ratio": 2.0, "max_drawdown": 0.15, "correlation_limit": 0.7, "sector_concentration_limit": 0.3, "position_sizing_method": "fixed_percentage", "emergency_exit": {"portfolio_loss_limit": 0.05, "vix_spike_threshold": 35, "market_crash_protection": true}}, "strategies": {"momentum_breakout": {"enabled": true, "lookback_period": 20, "volume_threshold": 1.5, "rsi_threshold": 60, "ma_period": 50, "min_price": 10, "max_price": 500}, "earnings_momentum": {"enabled": true, "earnings_surprise_threshold": 0.1, "hold_period_days": 7, "pre_earnings_buffer_days": 2}, "institutional_following": {"enabled": true, "min_position_size": 1000000, "follow_delay_days": 2, "13f_filing_sources": ["SEC", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "mean_reversion": {"enabled": false, "rsi_oversold": 30, "bollinger_std": 2, "hold_period_days": 3}}, "database": {"type": "postgresql", "host": "localhost", "port": 5432, "name": "schwab_trading_bot", "user": "trading_user", "password": "YOUR_DB_PASSWORD", "pool_size": 10, "max_overflow": 20}, "logging": {"level": "INFO", "format": "json", "file_path": "logs/trading_bot.log", "max_file_size": "10MB", "backup_count": 5, "sentry_dsn": "YOUR_SENTRY_DSN"}, "monitoring": {"prometheus_port": 8000, "health_check_interval": 300, "performance_metrics": true, "alert_thresholds": {"api_error_rate": 0.05, "order_failure_rate": 0.02, "system_memory_usage": 0.8}}, "deployment": {"environment": "development", "auto_restart": true, "graceful_shutdown_timeout": 30, "backup_frequency": "daily", "cloud_provider": "aws"}}