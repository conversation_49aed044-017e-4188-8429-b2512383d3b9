#!/usr/bin/env python3
"""
Schwab thinkorswim + Telegram Trading Bot
Main application entry point with comprehensive error handling and monitoring.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
License: MIT
"""

import asyncio
import signal
import sys
import os
from pathlib import Path
from typing import Optional
import structlog
from dotenv import load_dotenv

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.trading_bot import SchwabTelegramTradingBot
from src.utils.logger import setup_logging
from src.utils.database import DatabaseManager
from src.utils.rate_limiter import RateLimiter

# Load environment variables
load_dotenv()

# Setup structured logging
logger = setup_logging()


class TradingBotApplication:
    """Main application class with lifecycle management."""
    
    def __init__(self):
        self.bot: Optional[SchwabTelegramTradingBot] = None
        self.db_manager: Optional[DatabaseManager] = None
        self.shutdown_event = asyncio.Event()
        
    async def startup(self) -> None:
        """Initialize all components and start the trading bot."""
        try:
            logger.info("🚀 Starting Schwab Telegram Trading Bot...")
            
            # Initialize database
            self.db_manager = DatabaseManager()
            await self.db_manager.initialize()
            logger.info("✅ Database initialized successfully")
            
            # Initialize trading bot
            self.bot = SchwabTelegramTradingBot()
            await self.bot.initialize()
            logger.info("✅ Trading bot initialized successfully")
            
            # Setup signal handlers for graceful shutdown
            self._setup_signal_handlers()
            
            # Start the bot
            await self.bot.start()
            logger.info("🎯 Trading bot started successfully")
            
        except Exception as e:
            logger.error("❌ Failed to start trading bot", error=str(e), exc_info=True)
            await self.shutdown()
            raise
    
    async def run(self) -> None:
        """Main application loop."""
        try:
            await self.startup()
            
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
        except KeyboardInterrupt:
            logger.info("🛑 Received keyboard interrupt")
        except Exception as e:
            logger.error("💥 Unexpected error in main loop", error=str(e), exc_info=True)
        finally:
            await self.shutdown()
    
    async def shutdown(self) -> None:
        """Graceful shutdown of all components."""
        logger.info("🔄 Initiating graceful shutdown...")
        
        try:
            # Stop trading bot
            if self.bot:
                await self.bot.stop()
                logger.info("✅ Trading bot stopped")
            
            # Close database connections
            if self.db_manager:
                await self.db_manager.close()
                logger.info("✅ Database connections closed")
                
            logger.info("✅ Shutdown completed successfully")
            
        except Exception as e:
            logger.error("❌ Error during shutdown", error=str(e), exc_info=True)
    
    def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"📡 Received signal {signum}")
            self.shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, signal_handler)


def check_environment() -> bool:
    """Validate required environment variables."""
    required_vars = [
        'SCHWAB_CLIENT_ID',
        'SCHWAB_CLIENT_SECRET',
        'TELEGRAM_BOT_TOKEN',
        'TELEGRAM_CHAT_ID'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(
            "❌ Missing required environment variables",
            missing_vars=missing_vars
        )
        return False
    
    return True


def check_config_file() -> bool:
    """Check if configuration file exists."""
    config_path = Path("config.json")
    if not config_path.exists():
        logger.error(
            "❌ Configuration file not found",
            path=str(config_path),
            hint="Copy config.json.template to config.json and update with your settings"
        )
        return False
    
    return True


async def health_check() -> bool:
    """Perform basic health checks before starting."""
    logger.info("🔍 Performing health checks...")
    
    checks = [
        ("Environment variables", check_environment),
        ("Configuration file", check_config_file),
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        try:
            if check_func():
                logger.info(f"✅ {check_name} - OK")
            else:
                logger.error(f"❌ {check_name} - FAILED")
                all_passed = False
        except Exception as e:
            logger.error(f"❌ {check_name} - ERROR", error=str(e))
            all_passed = False
    
    return all_passed


async def main() -> None:
    """Main entry point."""
    try:
        # ASCII Art Banner
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║   🚀 Schwab thinkorswim + Telegram Trading Bot              ║
║                                                              ║
║   📈 Professional Algorithmic Trading Platform              ║
║   📱 Real-time Telegram Notifications                       ║
║   ⚡ Advanced Risk Management                                ║
║   🧠 Multiple Trading Strategies                            ║
║                                                              ║
║   Author: HectorTa1989                                       ║
║   GitHub: github.com/HectorTa1989                           ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
        
        # Perform health checks
        if not await health_check():
            logger.error("❌ Health checks failed. Please fix the issues and try again.")
            sys.exit(1)
        
        # Create and run application
        app = TradingBotApplication()
        await app.run()
        
    except Exception as e:
        logger.error("💥 Fatal error in main", error=str(e), exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    # Set event loop policy for Windows compatibility
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # Run the application
    asyncio.run(main())
