"""
Database Manager
Comprehensive database operations with connection pooling, migrations, and monitoring.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import os
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import structlog
from sqlalchemy import create_engine, text, MetaData, Table, Column, Integer, String, Float, DateTime, Boolean, Text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.pool import QueuePool
import json

logger = structlog.get_logger(__name__)

# Database models base
Base = declarative_base()


class Trade(Base):
    """Trade execution record."""
    __tablename__ = 'trades'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(10), nullable=False, index=True)
    side = Column(String(4), nullable=False)  # BUY/SELL
    quantity = Column(Integer, nullable=False)
    price = Column(Float, nullable=False)
    total_value = Column(Float, nullable=False)
    order_type = Column(String(20), nullable=False)
    strategy = Column(String(50), nullable=False, index=True)
    order_id = Column(String(50), unique=True, index=True)
    status = Column(String(20), default='PENDING', index=True)
    timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    execution_time = Column(DateTime)
    commission = Column(Float, default=0.0)
    notes = Column(Text)


class Position(Base):
    """Current position record."""
    __tablename__ = 'positions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(10), nullable=False, unique=True, index=True)
    quantity = Column(Integer, nullable=False)
    avg_cost = Column(Float, nullable=False)
    current_price = Column(Float)
    market_value = Column(Float)
    unrealized_pnl = Column(Float)
    unrealized_pnl_pct = Column(Float)
    day_pnl = Column(Float)
    strategy = Column(String(50), index=True)
    entry_date = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class Alert(Base):
    """Alert/notification record."""
    __tablename__ = 'alerts'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    alert_type = Column(String(50), nullable=False, index=True)
    priority = Column(String(20), default='normal', index=True)
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    sent_at = Column(DateTime, default=datetime.utcnow, index=True)
    telegram_message_id = Column(Integer)
    acknowledged = Column(Boolean, default=False)
    metadata = Column(Text)  # JSON string for additional data


class PerformanceMetric(Base):
    """Performance tracking record."""
    __tablename__ = 'performance_metrics'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    metric_name = Column(String(100), nullable=False, index=True)
    metric_value = Column(Float, nullable=False)
    metric_unit = Column(String(20))
    strategy = Column(String(50), index=True)
    symbol = Column(String(10), index=True)
    timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    metadata = Column(Text)  # JSON string for additional data


class DatabaseManager:
    """
    Comprehensive database manager with async support, connection pooling,
    and automatic migrations.
    """
    
    def __init__(self, database_url: str = None):
        """Initialize database manager."""
        self.database_url = database_url or self._get_database_url()
        self.engine = None
        self.async_engine = None
        self.session_factory = None
        self.async_session_factory = None
        
        # Connection pool settings
        self.pool_size = int(os.getenv('DB_POOL_SIZE', '10'))
        self.max_overflow = int(os.getenv('DB_MAX_OVERFLOW', '20'))
        self.pool_timeout = int(os.getenv('DB_POOL_TIMEOUT', '30'))
        
        # Performance tracking
        self.query_stats = {
            'total_queries': 0,
            'successful_queries': 0,
            'failed_queries': 0,
            'avg_query_time': 0.0
        }
    
    def _get_database_url(self) -> str:
        """Get database URL from environment variables."""
        # Try DATABASE_URL first (common in cloud deployments)
        database_url = os.getenv('DATABASE_URL')
        if database_url:
            return database_url
        
        # Build from individual components
        db_type = os.getenv('DB_TYPE', 'postgresql')
        db_host = os.getenv('DB_HOST', 'localhost')
        db_port = os.getenv('DB_PORT', '5432')
        db_name = os.getenv('DB_NAME', 'schwab_trading_bot')
        db_user = os.getenv('DB_USER', 'trading_user')
        db_password = os.getenv('DB_PASSWORD', '')
        
        if db_type == 'sqlite':
            return f"sqlite:///data/{db_name}.db"
        else:
            return f"{db_type}://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    async def initialize(self) -> None:
        """Initialize database connections and create tables."""
        try:
            logger.info("🔧 Initializing database connections...")
            
            # Create sync engine for migrations
            self.engine = create_engine(
                self.database_url,
                poolclass=QueuePool,
                pool_size=self.pool_size,
                max_overflow=self.max_overflow,
                pool_timeout=self.pool_timeout,
                pool_pre_ping=True,
                echo=os.getenv('DB_ECHO', 'false').lower() == 'true'
            )
            
            # Create async engine for operations
            async_url = self.database_url.replace('postgresql://', 'postgresql+asyncpg://')
            self.async_engine = create_async_engine(
                async_url,
                poolclass=QueuePool,
                pool_size=self.pool_size,
                max_overflow=self.max_overflow,
                pool_timeout=self.pool_timeout,
                pool_pre_ping=True,
                echo=os.getenv('DB_ECHO', 'false').lower() == 'true'
            )
            
            # Create session factories
            self.session_factory = sessionmaker(bind=self.engine)
            self.async_session_factory = async_sessionmaker(
                bind=self.async_engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # Create tables
            await self._create_tables()
            
            # Test connection
            await self._test_connection()
            
            logger.info("✅ Database initialized successfully")
            
        except Exception as e:
            logger.error("❌ Failed to initialize database", error=str(e), exc_info=True)
            raise
    
    async def _create_tables(self) -> None:
        """Create database tables if they don't exist."""
        try:
            # Use sync engine for table creation
            Base.metadata.create_all(self.engine)
            logger.info("✅ Database tables created/verified")
        except Exception as e:
            logger.error("❌ Failed to create tables", error=str(e))
            raise
    
    async def _test_connection(self) -> None:
        """Test database connection."""
        try:
            async with self.async_engine.begin() as conn:
                result = await conn.execute(text("SELECT 1"))
                await result.fetchone()
            logger.info("✅ Database connection test successful")
        except Exception as e:
            logger.error("❌ Database connection test failed", error=str(e))
            raise
    
    async def log_trade(self, trade_data: Dict[str, Any]) -> int:
        """Log a trade execution."""
        try:
            async with self.async_session_factory() as session:
                trade = Trade(
                    symbol=trade_data['symbol'],
                    side=trade_data['side'],
                    quantity=trade_data['quantity'],
                    price=trade_data['price'],
                    total_value=trade_data['quantity'] * trade_data['price'],
                    order_type=trade_data.get('order_type', 'MARKET'),
                    strategy=trade_data['strategy'],
                    order_id=trade_data.get('order_id'),
                    status=trade_data.get('status', 'PENDING'),
                    commission=trade_data.get('commission', 0.0),
                    notes=trade_data.get('notes')
                )
                
                session.add(trade)
                await session.commit()
                await session.refresh(trade)
                
                self.query_stats['successful_queries'] += 1
                logger.info("📝 Trade logged", trade_id=trade.id, symbol=trade.symbol)
                
                return trade.id
                
        except Exception as e:
            self.query_stats['failed_queries'] += 1
            logger.error("❌ Failed to log trade", error=str(e), trade_data=trade_data)
            raise
    
    async def update_position(self, position_data: Dict[str, Any]) -> None:
        """Update or create position record."""
        try:
            async with self.async_session_factory() as session:
                # Try to find existing position
                result = await session.execute(
                    text("SELECT * FROM positions WHERE symbol = :symbol"),
                    {"symbol": position_data['symbol']}
                )
                existing = result.fetchone()
                
                if existing:
                    # Update existing position
                    await session.execute(
                        text("""
                            UPDATE positions SET
                                quantity = :quantity,
                                avg_cost = :avg_cost,
                                current_price = :current_price,
                                market_value = :market_value,
                                unrealized_pnl = :unrealized_pnl,
                                unrealized_pnl_pct = :unrealized_pnl_pct,
                                day_pnl = :day_pnl,
                                updated_at = :updated_at
                            WHERE symbol = :symbol
                        """),
                        {
                            **position_data,
                            'updated_at': datetime.utcnow()
                        }
                    )
                else:
                    # Create new position
                    position = Position(**position_data)
                    session.add(position)
                
                await session.commit()
                self.query_stats['successful_queries'] += 1
                
        except Exception as e:
            self.query_stats['failed_queries'] += 1
            logger.error("❌ Failed to update position", error=str(e))
            raise
    
    async def log_alert(self, alert_data: Dict[str, Any]) -> int:
        """Log an alert/notification."""
        try:
            async with self.async_session_factory() as session:
                alert = Alert(
                    alert_type=alert_data['alert_type'],
                    priority=alert_data.get('priority', 'normal'),
                    title=alert_data['title'],
                    message=alert_data['message'],
                    telegram_message_id=alert_data.get('telegram_message_id'),
                    metadata=json.dumps(alert_data.get('metadata', {}))
                )
                
                session.add(alert)
                await session.commit()
                await session.refresh(alert)
                
                self.query_stats['successful_queries'] += 1
                return alert.id
                
        except Exception as e:
            self.query_stats['failed_queries'] += 1
            logger.error("❌ Failed to log alert", error=str(e))
            raise
    
    async def log_performance_metric(self, metric_data: Dict[str, Any]) -> None:
        """Log a performance metric."""
        try:
            async with self.async_session_factory() as session:
                metric = PerformanceMetric(
                    metric_name=metric_data['metric_name'],
                    metric_value=metric_data['metric_value'],
                    metric_unit=metric_data.get('metric_unit'),
                    strategy=metric_data.get('strategy'),
                    symbol=metric_data.get('symbol'),
                    metadata=json.dumps(metric_data.get('metadata', {}))
                )
                
                session.add(metric)
                await session.commit()
                
                self.query_stats['successful_queries'] += 1
                
        except Exception as e:
            self.query_stats['failed_queries'] += 1
            logger.error("❌ Failed to log performance metric", error=str(e))
            raise
    
    async def get_trades(
        self,
        symbol: str = None,
        strategy: str = None,
        start_date: datetime = None,
        end_date: datetime = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get trade records with filtering."""
        try:
            async with self.async_session_factory() as session:
                query = "SELECT * FROM trades WHERE 1=1"
                params = {}
                
                if symbol:
                    query += " AND symbol = :symbol"
                    params['symbol'] = symbol
                
                if strategy:
                    query += " AND strategy = :strategy"
                    params['strategy'] = strategy
                
                if start_date:
                    query += " AND timestamp >= :start_date"
                    params['start_date'] = start_date
                
                if end_date:
                    query += " AND timestamp <= :end_date"
                    params['end_date'] = end_date
                
                query += " ORDER BY timestamp DESC LIMIT :limit"
                params['limit'] = limit
                
                result = await session.execute(text(query), params)
                trades = result.fetchall()
                
                self.query_stats['successful_queries'] += 1
                
                return [dict(trade._mapping) for trade in trades]
                
        except Exception as e:
            self.query_stats['failed_queries'] += 1
            logger.error("❌ Failed to get trades", error=str(e))
            raise
    
    async def get_positions(self) -> List[Dict[str, Any]]:
        """Get all current positions."""
        try:
            async with self.async_session_factory() as session:
                result = await session.execute(
                    text("SELECT * FROM positions ORDER BY market_value DESC")
                )
                positions = result.fetchall()
                
                self.query_stats['successful_queries'] += 1
                
                return [dict(position._mapping) for position in positions]
                
        except Exception as e:
            self.query_stats['failed_queries'] += 1
            logger.error("❌ Failed to get positions", error=str(e))
            raise
    
    async def cleanup_old_data(self, days_to_keep: int = 90) -> None:
        """Clean up old data to manage database size."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
            
            async with self.async_session_factory() as session:
                # Clean old alerts
                await session.execute(
                    text("DELETE FROM alerts WHERE sent_at < :cutoff_date"),
                    {"cutoff_date": cutoff_date}
                )
                
                # Clean old performance metrics
                await session.execute(
                    text("DELETE FROM performance_metrics WHERE timestamp < :cutoff_date"),
                    {"cutoff_date": cutoff_date}
                )
                
                await session.commit()
                
            logger.info("🧹 Old data cleaned up", cutoff_date=cutoff_date)
            
        except Exception as e:
            logger.error("❌ Failed to cleanup old data", error=str(e))
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        try:
            async with self.async_session_factory() as session:
                # Get table counts
                trades_count = await session.execute(text("SELECT COUNT(*) FROM trades"))
                positions_count = await session.execute(text("SELECT COUNT(*) FROM positions"))
                alerts_count = await session.execute(text("SELECT COUNT(*) FROM alerts"))
                
                return {
                    'trades_count': trades_count.scalar(),
                    'positions_count': positions_count.scalar(),
                    'alerts_count': alerts_count.scalar(),
                    'query_stats': self.query_stats
                }
                
        except Exception as e:
            logger.error("❌ Failed to get database stats", error=str(e))
            return {'error': str(e)}
    
    async def close(self) -> None:
        """Close database connections."""
        try:
            if self.async_engine:
                await self.async_engine.dispose()
            
            if self.engine:
                self.engine.dispose()
            
            logger.info("✅ Database connections closed")
            
        except Exception as e:
            logger.error("❌ Error closing database connections", error=str(e))
