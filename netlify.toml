# Netlify configuration for Schwab Telegram Trading Bot
# Optimized for serverless deployment with edge functions

[build]
  # Build command for the application
  command = "pip install -r requirements.txt && python -m pytest tests/ && python setup.py build"
  
  # Publish directory (for static assets if any)
  publish = "dist/"
  
  # Build environment
  environment = { PYTHON_VERSION = "3.11", NODE_VERSION = "18" }

[build.processing]
  # Skip processing for certain file types
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true

# Environment variables for different deploy contexts
[context.production.environment]
  ENVIRONMENT = "production"
  LOG_LEVEL = "INFO"
  PAPER_TRADING = "false"
  ENABLE_METRICS = "true"

[context.deploy-preview.environment]
  ENVIRONMENT = "staging"
  LOG_LEVEL = "DEBUG"
  PAPER_TRADING = "true"
  ENABLE_METRICS = "true"

[context.branch-deploy.environment]
  ENVIRONMENT = "development"
  LOG_LEVEL = "DEBUG"
  PAPER_TRADING = "true"
  ENABLE_METRICS = "false"

# Serverless functions configuration
[functions]
  # Directory for serverless functions
  directory = "netlify/functions/"
  
  # Runtime settings
  node_bundler = "esbuild"
  
  # External node modules to include
  external_node_modules = ["sharp", "canvas"]

# Edge functions configuration
[edge_functions]
  # Directory for edge functions
  directory = "netlify/edge-functions/"

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://api.schwabapi.com https://api.telegram.org"
    
    # Performance headers
    Cache-Control = "public, max-age=31536000, immutable"
    
    # CORS headers for API endpoints
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization"

# API routes configuration
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

[[redirects]]
  from = "/health"
  to = "/.netlify/functions/health"
  status = 200

[[redirects]]
  from = "/metrics"
  to = "/.netlify/functions/metrics"
  status = 200

# Webhook endpoints
[[redirects]]
  from = "/webhook/schwab"
  to = "/.netlify/functions/schwab-webhook"
  status = 200

[[redirects]]
  from = "/webhook/telegram"
  to = "/.netlify/functions/telegram-webhook"
  status = 200

# SPA fallback for dashboard
[[redirects]]
  from = "/dashboard/*"
  to = "/dashboard/index.html"
  status = 200

# Error pages
[[redirects]]
  from = "/*"
  to = "/404.html"
  status = 404

# Form handling
[forms]
  # Enable form processing
  enabled = true

# Large Media configuration
[large_media]
  # Enable Git LFS for large files
  enabled = true

# Plugin configuration
[[plugins]]
  package = "@netlify/plugin-sitemap"
  
  [plugins.inputs]
    buildDir = "dist"
    exclude = [
      "admin/**/*",
      "private/**/*"
    ]

[[plugins]]
  package = "netlify-plugin-lighthouse"
  
  [plugins.inputs]
    audits = ["performance", "accessibility", "best-practices", "seo"]

# Split testing configuration
[split_testing]
  # Enable split testing
  enabled = true

# Analytics
[analytics]
  # Enable analytics
  enabled = true

# Build hooks for CI/CD
[build.hooks]
  # Pre-build hook
  pre_build = "echo 'Starting build process...'"
  
  # Post-build hook
  post_build = "echo 'Build completed successfully!'"

# Development server configuration
[dev]
  # Local development settings
  command = "python main.py"
  port = 8000
  publish = "dist"
  
  # Environment variables for development
  [dev.env]
    ENVIRONMENT = "development"
    DEBUG = "true"
    LOG_LEVEL = "DEBUG"
