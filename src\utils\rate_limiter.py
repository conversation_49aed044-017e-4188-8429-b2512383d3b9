"""
Rate Limiter
Advanced rate limiting with token bucket algorithm and burst handling.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import asyncio
import time
from typing import Optional, Dict, Any
from collections import deque
import structlog

logger = structlog.get_logger(__name__)


class RateLimiter:
    """
    Advanced rate limiter using token bucket algorithm with burst support.
    Handles API rate limits for Schwab (120 req/min) and other services.
    """
    
    def __init__(
        self,
        max_calls: int,
        time_window: int = 60,
        burst_limit: Optional[int] = None,
        backoff_factor: float = 1.5
    ):
        """
        Initialize rate limiter.
        
        Args:
            max_calls: Maximum calls allowed in time window
            time_window: Time window in seconds (default: 60)
            burst_limit: Maximum burst calls (default: max_calls // 4)
            backoff_factor: Exponential backoff factor for retries
        """
        self.max_calls = max_calls
        self.time_window = time_window
        self.burst_limit = burst_limit or max_calls // 4
        self.backoff_factor = backoff_factor
        
        # Token bucket parameters
        self.tokens = max_calls
        self.last_refill = time.time()
        self.refill_rate = max_calls / time_window  # tokens per second
        
        # Request tracking
        self.request_times = deque()
        self.total_requests = 0
        self.blocked_requests = 0
        self.burst_requests = 0
        
        # Async lock for thread safety
        self._lock = asyncio.Lock()
        
        logger.info(
            "✅ Rate limiter initialized",
            max_calls=max_calls,
            time_window=time_window,
            burst_limit=self.burst_limit
        )
    
    async def acquire(self, tokens_needed: int = 1) -> bool:
        """
        Acquire tokens from the bucket.
        
        Args:
            tokens_needed: Number of tokens required
            
        Returns:
            True if tokens acquired, False if rate limited
        """
        async with self._lock:
            current_time = time.time()
            
            # Refill tokens based on elapsed time
            await self._refill_tokens(current_time)
            
            # Check if we have enough tokens
            if self.tokens >= tokens_needed:
                self.tokens -= tokens_needed
                self.total_requests += 1
                self.request_times.append(current_time)
                
                # Clean old request times
                self._cleanup_old_requests(current_time)
                
                logger.debug(
                    "🟢 Tokens acquired",
                    tokens_used=tokens_needed,
                    tokens_remaining=self.tokens
                )
                return True
            
            # Check if burst is available
            if tokens_needed <= self.burst_limit and len(self.request_times) == 0:
                self.tokens = max(0, self.tokens - tokens_needed)
                self.total_requests += 1
                self.burst_requests += 1
                self.request_times.append(current_time)
                
                logger.warning(
                    "🟡 Burst tokens used",
                    tokens_used=tokens_needed,
                    burst_count=self.burst_requests
                )
                return True
            
            # Rate limited
            self.blocked_requests += 1
            wait_time = self._calculate_wait_time()
            
            logger.warning(
                "🔴 Rate limited",
                tokens_needed=tokens_needed,
                tokens_available=self.tokens,
                wait_time=wait_time
            )
            
            return False
    
    async def wait_for_token(self, tokens_needed: int = 1, max_wait: float = 300.0) -> bool:
        """
        Wait for tokens to become available.
        
        Args:
            tokens_needed: Number of tokens required
            max_wait: Maximum wait time in seconds
            
        Returns:
            True if tokens acquired, False if timeout
        """
        start_time = time.time()
        attempt = 0
        
        while time.time() - start_time < max_wait:
            if await self.acquire(tokens_needed):
                return True
            
            # Calculate exponential backoff wait time
            wait_time = min(
                self._calculate_wait_time() * (self.backoff_factor ** attempt),
                30.0  # Max 30 seconds between attempts
            )
            
            logger.info(
                "⏳ Waiting for rate limit",
                wait_time=wait_time,
                attempt=attempt + 1
            )
            
            await asyncio.sleep(wait_time)
            attempt += 1
        
        logger.error("❌ Rate limiter timeout", max_wait=max_wait)
        return False
    
    async def _refill_tokens(self, current_time: float) -> None:
        """Refill tokens based on elapsed time."""
        time_elapsed = current_time - self.last_refill
        
        if time_elapsed > 0:
            tokens_to_add = time_elapsed * self.refill_rate
            self.tokens = min(self.max_calls, self.tokens + tokens_to_add)
            self.last_refill = current_time
    
    def _cleanup_old_requests(self, current_time: float) -> None:
        """Remove request times older than the time window."""
        cutoff_time = current_time - self.time_window
        
        while self.request_times and self.request_times[0] < cutoff_time:
            self.request_times.popleft()
    
    def _calculate_wait_time(self) -> float:
        """Calculate optimal wait time based on current state."""
        if not self.request_times:
            return 1.0
        
        # Time until oldest request expires
        current_time = time.time()
        oldest_request = self.request_times[0]
        time_until_expire = (oldest_request + self.time_window) - current_time
        
        # Time until next token refill
        tokens_needed = 1
        time_until_refill = tokens_needed / self.refill_rate
        
        return max(0.1, min(time_until_expire, time_until_refill))
    
    def get_stats(self) -> Dict[str, Any]:
        """Get rate limiter statistics."""
        current_time = time.time()
        self._cleanup_old_requests(current_time)
        
        return {
            'max_calls': self.max_calls,
            'time_window': self.time_window,
            'current_tokens': self.tokens,
            'requests_in_window': len(self.request_times),
            'total_requests': self.total_requests,
            'blocked_requests': self.blocked_requests,
            'burst_requests': self.burst_requests,
            'success_rate': (
                (self.total_requests - self.blocked_requests) / max(1, self.total_requests)
            ) * 100,
            'utilization': (len(self.request_times) / self.max_calls) * 100
        }
    
    def reset_stats(self) -> None:
        """Reset statistics counters."""
        self.total_requests = 0
        self.blocked_requests = 0
        self.burst_requests = 0
        logger.info("🔄 Rate limiter stats reset")
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on rate limiter."""
        stats = self.get_stats()
        
        health_status = {
            'healthy': True,
            'issues': [],
            'stats': stats
        }
        
        # Check for high utilization
        if stats['utilization'] > 90:
            health_status['healthy'] = False
            health_status['issues'].append('High utilization (>90%)')
        
        # Check for high block rate
        if stats['success_rate'] < 95:
            health_status['healthy'] = False
            health_status['issues'].append('High block rate (<95% success)')
        
        # Check for excessive burst usage
        burst_rate = (stats['burst_requests'] / max(1, stats['total_requests'])) * 100
        if burst_rate > 20:
            health_status['healthy'] = False
            health_status['issues'].append('Excessive burst usage (>20%)')
        
        return health_status
    
    def __str__(self) -> str:
        """String representation of rate limiter."""
        stats = self.get_stats()
        return (
            f"RateLimiter(max_calls={self.max_calls}, "
            f"tokens={stats['current_tokens']:.1f}, "
            f"utilization={stats['utilization']:.1f}%)"
        )


class AdaptiveRateLimiter(RateLimiter):
    """
    Adaptive rate limiter that adjusts limits based on API responses.
    Useful for APIs that return rate limit information in headers.
    """
    
    def __init__(self, *args, **kwargs):
        """Initialize adaptive rate limiter."""
        super().__init__(*args, **kwargs)
        
        # Adaptive parameters
        self.adaptive_enabled = True
        self.min_calls = max(1, self.max_calls // 4)
        self.adaptation_factor = 0.1
        self.last_429_time: Optional[float] = None
        
        logger.info("✅ Adaptive rate limiter initialized")
    
    async def handle_429_response(self, retry_after: Optional[int] = None) -> None:
        """
        Handle 429 (Too Many Requests) response.
        
        Args:
            retry_after: Retry-After header value in seconds
        """
        async with self._lock:
            self.last_429_time = time.time()
            
            if self.adaptive_enabled:
                # Reduce rate limit by adaptation factor
                old_max_calls = self.max_calls
                self.max_calls = max(
                    self.min_calls,
                    int(self.max_calls * (1 - self.adaptation_factor))
                )
                
                # Update refill rate
                self.refill_rate = self.max_calls / self.time_window
                
                logger.warning(
                    "🔻 Rate limit adapted down",
                    old_limit=old_max_calls,
                    new_limit=self.max_calls,
                    retry_after=retry_after
                )
            
            # Wait for retry-after period if specified
            if retry_after:
                await asyncio.sleep(retry_after)
    
    async def handle_success_response(self, remaining_calls: Optional[int] = None) -> None:
        """
        Handle successful API response.
        
        Args:
            remaining_calls: Remaining calls from API headers
        """
        if not self.adaptive_enabled:
            return
        
        async with self._lock:
            current_time = time.time()
            
            # Gradually increase rate limit if no recent 429s
            if (not self.last_429_time or 
                current_time - self.last_429_time > self.time_window * 2):
                
                old_max_calls = self.max_calls
                original_max = self.max_calls / (1 - self.adaptation_factor)
                
                self.max_calls = min(
                    int(original_max),
                    int(self.max_calls * (1 + self.adaptation_factor / 2))
                )
                
                if self.max_calls != old_max_calls:
                    self.refill_rate = self.max_calls / self.time_window
                    
                    logger.info(
                        "🔺 Rate limit adapted up",
                        old_limit=old_max_calls,
                        new_limit=self.max_calls
                    )
            
            # Update tokens based on remaining calls if available
            if remaining_calls is not None:
                self.tokens = min(self.tokens, remaining_calls)
