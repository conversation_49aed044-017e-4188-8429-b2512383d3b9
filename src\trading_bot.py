"""
Main Trading Bot Class
Orchestrates all trading operations, strategies, and notifications.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import asyncio
import json
from datetime import datetime, time
from typing import Dict, List, Optional, Any
import structlog
from pathlib import Path

from .schwab_client import SchwabClient
from .telegram_notifier import TelegramNotifier
from .strategies.momentum_strategy import MomentumStrategy
from .strategies.earnings_strategy import EarningsStrategy
from .strategies.institutional_strategy import InstitutionalStrategy
from .risk_management.risk_manager import RiskManager
from .analysis.market_analyzer import MarketAnalyzer
from .utils.database import DatabaseManager
from .utils.rate_limiter import RateLimiter

logger = structlog.get_logger(__name__)


class SchwabTelegramTradingBot:
    """
    Main trading bot orchestrator with comprehensive strategy management,
    risk controls, and real-time notifications.
    """
    
    def __init__(self, config_path: str = "config.json"):
        """Initialize the trading bot with configuration."""
        self.config = self._load_config(config_path)
        self.is_running = False
        self.is_market_hours = False
        
        # Core components
        self.schwab_client: Optional[SchwabClient] = None
        self.telegram_notifier: Optional[TelegramNotifier] = None
        self.risk_manager: Optional[RiskManager] = None
        self.market_analyzer: Optional[MarketAnalyzer] = None
        self.db_manager: Optional[DatabaseManager] = None
        
        # Trading strategies
        self.strategies: Dict[str, Any] = {}
        
        # Rate limiter for API calls
        self.rate_limiter = RateLimiter(
            max_calls=self.config['schwab']['rate_limit']['requests_per_minute'],
            time_window=60
        )
        
        # Performance tracking
        self.performance_metrics = {
            'trades_today': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_pnl': 0.0,
            'start_time': datetime.now()
        }
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from JSON file."""
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            logger.info("✅ Configuration loaded successfully", path=config_path)
            return config
        except FileNotFoundError:
            logger.error("❌ Configuration file not found", path=config_path)
            raise
        except json.JSONDecodeError as e:
            logger.error("❌ Invalid JSON in configuration file", error=str(e))
            raise
    
    async def initialize(self) -> None:
        """Initialize all bot components."""
        try:
            logger.info("🔧 Initializing trading bot components...")
            
            # Initialize database
            self.db_manager = DatabaseManager()
            await self.db_manager.initialize()
            
            # Initialize Schwab client
            self.schwab_client = SchwabClient(
                client_id=self.config['schwab']['client_id'],
                client_secret=self.config['schwab']['client_secret'],
                redirect_uri=self.config['schwab']['redirect_uri'],
                account_number=self.config['schwab']['account_number']
            )
            await self.schwab_client.initialize()
            
            # Initialize Telegram notifier
            self.telegram_notifier = TelegramNotifier(
                bot_token=self.config['telegram']['bot_token'],
                chat_id=self.config['telegram']['chat_id']
            )
            await self.telegram_notifier.initialize()
            
            # Initialize risk manager
            self.risk_manager = RiskManager(
                config=self.config['risk_management'],
                schwab_client=self.schwab_client
            )
            
            # Initialize market analyzer
            self.market_analyzer = MarketAnalyzer(
                config=self.config['trading']['market_data']
            )
            
            # Initialize trading strategies
            await self._initialize_strategies()
            
            # Send startup notification
            await self.telegram_notifier.send_system_alert(
                "startup",
                "🚀 Trading bot initialized successfully!\n"
                f"📊 Paper Trading: {'ON' if self.config['trading']['paper_trading'] else 'OFF'}\n"
                f"⚡ Strategies: {len(self.strategies)} active\n"
                f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            
            logger.info("✅ All components initialized successfully")
            
        except Exception as e:
            logger.error("❌ Failed to initialize trading bot", error=str(e), exc_info=True)
            raise
    
    async def _initialize_strategies(self) -> None:
        """Initialize enabled trading strategies."""
        strategy_configs = self.config['strategies']
        enabled_strategies = self.config['trading']['strategies_enabled']
        
        strategy_classes = {
            'momentum_breakout': MomentumStrategy,
            'earnings_momentum': EarningsStrategy,
            'institutional_following': InstitutionalStrategy
        }
        
        for strategy_name in enabled_strategies:
            if strategy_name in strategy_classes and strategy_configs.get(strategy_name, {}).get('enabled', False):
                strategy_class = strategy_classes[strategy_name]
                strategy = strategy_class(
                    config=strategy_configs[strategy_name],
                    schwab_client=self.schwab_client,
                    market_analyzer=self.market_analyzer,
                    risk_manager=self.risk_manager
                )
                self.strategies[strategy_name] = strategy
                logger.info(f"✅ Initialized strategy: {strategy_name}")
    
    async def start(self) -> None:
        """Start the trading bot main loop."""
        self.is_running = True
        logger.info("🎯 Starting trading bot main loop...")
        
        # Start background tasks
        tasks = [
            asyncio.create_task(self._market_hours_monitor()),
            asyncio.create_task(self._trading_loop()),
            asyncio.create_task(self._portfolio_monitor()),
            asyncio.create_task(self._performance_tracker())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error("❌ Error in main trading loop", error=str(e), exc_info=True)
            await self.stop()
    
    async def stop(self) -> None:
        """Stop the trading bot gracefully."""
        logger.info("🛑 Stopping trading bot...")
        self.is_running = False
        
        # Send shutdown notification
        if self.telegram_notifier:
            await self.telegram_notifier.send_system_alert(
                "shutdown",
                "🔄 Trading bot shutting down gracefully...\n"
                f"📊 Session Summary:\n"
                f"• Trades Today: {self.performance_metrics['trades_today']}\n"
                f"• Success Rate: {self._calculate_success_rate():.1f}%\n"
                f"• Runtime: {self._calculate_runtime()}"
            )
        
        logger.info("✅ Trading bot stopped successfully")
    
    async def _market_hours_monitor(self) -> None:
        """Monitor market hours and update trading status."""
        while self.is_running:
            try:
                current_time = datetime.now().time()
                trading_config = self.config['trading']['trading_hours']
                
                start_time = time.fromisoformat(trading_config['start'])
                end_time = time.fromisoformat(trading_config['end'])
                
                was_market_hours = self.is_market_hours
                self.is_market_hours = start_time <= current_time <= end_time
                
                # Market open notification
                if self.is_market_hours and not was_market_hours:
                    await self.telegram_notifier.send_system_alert(
                        "market_open",
                        "🔔 Market is now OPEN!\n"
                        "🎯 Trading strategies activated\n"
                        "📊 Monitoring for opportunities..."
                    )
                    logger.info("📈 Market opened - Trading activated")
                
                # Market close notification
                elif not self.is_market_hours and was_market_hours:
                    await self.telegram_notifier.send_system_alert(
                        "market_close",
                        "🔕 Market is now CLOSED\n"
                        "📊 Daily summary will be sent shortly..."
                    )
                    await self._send_daily_summary()
                    logger.info("📉 Market closed - Trading deactivated")
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error("❌ Error in market hours monitor", error=str(e))
                await asyncio.sleep(60)
    
    async def _trading_loop(self) -> None:
        """Main trading strategy execution loop."""
        while self.is_running:
            try:
                if not self.is_market_hours:
                    await asyncio.sleep(60)
                    continue
                
                # Execute strategies
                for strategy_name, strategy in self.strategies.items():
                    try:
                        await self.rate_limiter.acquire()
                        signals = await strategy.generate_signals()
                        
                        for signal in signals:
                            await self._process_trading_signal(signal, strategy_name)
                            
                    except Exception as e:
                        logger.error(f"❌ Error in strategy {strategy_name}", error=str(e))
                
                # Wait before next iteration
                await asyncio.sleep(self.config['trading']['market_data']['update_frequency'])
                
            except Exception as e:
                logger.error("❌ Error in trading loop", error=str(e))
                await asyncio.sleep(60)
    
    async def _process_trading_signal(self, signal: Dict[str, Any], strategy_name: str) -> None:
        """Process a trading signal from a strategy."""
        try:
            # Risk validation
            risk_check = await self.risk_manager.validate_trade(signal)
            if not risk_check['approved']:
                logger.warning(
                    "⚠️ Trade rejected by risk manager",
                    symbol=signal['symbol'],
                    reason=risk_check['reason']
                )
                return
            
            # Calculate position size
            position_size = await self.risk_manager.calculate_position_size(
                signal['symbol'],
                signal['entry_price'],
                signal.get('stop_loss')
            )
            
            # Place order
            order_result = await self.schwab_client.place_order(
                symbol=signal['symbol'],
                quantity=position_size,
                order_type=signal['order_type'],
                side=signal['side'],
                price=signal.get('price')
            )
            
            if order_result['success']:
                # Log trade
                await self.db_manager.log_trade({
                    'symbol': signal['symbol'],
                    'side': signal['side'],
                    'quantity': position_size,
                    'price': signal.get('price'),
                    'strategy': strategy_name,
                    'order_id': order_result['order_id']
                })
                
                # Send notification
                await self.telegram_notifier.send_trade_notification({
                    'symbol': signal['symbol'],
                    'action': signal['side'],
                    'quantity': position_size,
                    'price': signal.get('price'),
                    'strategy': strategy_name,
                    'order_id': order_result['order_id']
                })
                
                self.performance_metrics['trades_today'] += 1
                self.performance_metrics['successful_trades'] += 1
                
                logger.info(
                    "✅ Trade executed successfully",
                    symbol=signal['symbol'],
                    side=signal['side'],
                    quantity=position_size,
                    strategy=strategy_name
                )
            else:
                self.performance_metrics['failed_trades'] += 1
                logger.error(
                    "❌ Trade execution failed",
                    symbol=signal['symbol'],
                    error=order_result.get('error')
                )
                
        except Exception as e:
            logger.error("❌ Error processing trading signal", error=str(e), exc_info=True)
    
    def _calculate_success_rate(self) -> float:
        """Calculate trading success rate."""
        total_trades = self.performance_metrics['successful_trades'] + self.performance_metrics['failed_trades']
        if total_trades == 0:
            return 0.0
        return (self.performance_metrics['successful_trades'] / total_trades) * 100
    
    def _calculate_runtime(self) -> str:
        """Calculate bot runtime."""
        runtime = datetime.now() - self.performance_metrics['start_time']
        hours, remainder = divmod(runtime.total_seconds(), 3600)
        minutes, _ = divmod(remainder, 60)
        return f"{int(hours)}h {int(minutes)}m"
