# Schwab API Credentials
SCHWAB_CLIENT_ID=your_schwab_client_id_here
SCHWAB_CLIENT_SECRET=your_schwab_client_secret_here
SCHWAB_REDIRECT_URI=http://localhost:8080/callback
SCHWAB_ACCOUNT_NUMBER=your_account_number_here

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# Database Configuration
DATABASE_URL=postgresql://trading_user:password@localhost:5432/schwab_trading_bot
DB_HOST=localhost
DB_PORT=5432
DB_NAME=schwab_trading_bot
DB_USER=trading_user
DB_PASSWORD=your_database_password_here

# Security & Encryption
SECRET_KEY=your_secret_key_for_encryption_here
JWT_SECRET=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here

# External APIs
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
FINNHUB_API_KEY=your_finnhub_key_here
POLYGON_API_KEY=your_polygon_key_here

# Monitoring & Logging
SENTRY_DSN=your_sentry_dsn_here
LOG_LEVEL=INFO
PROMETHEUS_PORT=8000

# Cloud Deployment
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1
AWS_S3_BUCKET=schwab-trading-bot-data

GCP_PROJECT_ID=your_gcp_project_id_here
GCP_SERVICE_ACCOUNT_KEY=path/to/service-account-key.json

AZURE_STORAGE_CONNECTION_STRING=your_azure_connection_string_here

# Application Settings
ENVIRONMENT=development
DEBUG=true
PAPER_TRADING=true
MAX_POSITIONS=10
MAX_POSITION_SIZE=0.05

# Rate Limiting
SCHWAB_RATE_LIMIT=120
API_BURST_LIMIT=10

# Trading Hours
TRADING_START_TIME=09:30
TRADING_END_TIME=16:00
TIMEZONE=US/Eastern

# Risk Management
MAX_PORTFOLIO_RISK=0.02
STOP_LOSS_PCT=0.02
TAKE_PROFIT_RATIO=2.0
MAX_DRAWDOWN=0.15

# Notification Settings
ALERT_FREQUENCY=immediate
ENABLE_TRADE_ALERTS=true
ENABLE_PORTFOLIO_UPDATES=true
ENABLE_RISK_ALERTS=true

# Performance Monitoring
ENABLE_METRICS=true
METRICS_RETENTION_DAYS=90
BACKUP_FREQUENCY=daily

# Development Settings
FLASK_ENV=development
FLASK_DEBUG=true
TESTING=false
