"""
Base Trading Strategy
Abstract base class for all trading strategies with common functionality.

Author: Hector<PERSON>a1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import structlog
import pandas as pd
import numpy as np

logger = structlog.get_logger(__name__)


class BaseStrategy(ABC):
    """
    Abstract base class for all trading strategies.
    Provides common functionality and enforces strategy interface.
    """
    
    def __init__(
        self,
        config: Dict[str, Any],
        schwab_client,
        market_analyzer,
        risk_manager,
        name: str = "BaseStrategy"
    ):
        """Initialize base strategy."""
        self.config = config
        self.schwab_client = schwab_client
        self.market_analyzer = market_analyzer
        self.risk_manager = risk_manager
        self.name = name
        
        # Strategy state
        self.is_enabled = config.get('enabled', True)
        self.last_signal_time: Optional[datetime] = None
        self.active_positions: Dict[str, Dict] = {}
        
        # Performance tracking
        self.performance_metrics = {
            'total_signals': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'avg_return': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0
        }
        
        # Signal history for analysis
        self.signal_history: List[Dict] = []
        
        logger.info(f"✅ {self.name} strategy initialized", enabled=self.is_enabled)
    
    @abstractmethod
    async def generate_signals(self) -> List[Dict[str, Any]]:
        """
        Generate trading signals based on strategy logic.
        Must be implemented by each strategy.
        
        Returns:
            List of signal dictionaries with required fields:
            - symbol: str
            - side: str ('BUY' or 'SELL')
            - order_type: str ('MARKET', 'LIMIT', 'STOP')
            - entry_price: float
            - stop_loss: float (optional)
            - take_profit: float (optional)
            - confidence: float (0.0 to 1.0)
            - reason: str
        """
        pass
    
    @abstractmethod
    async def should_exit_position(self, symbol: str, position_data: Dict) -> Tuple[bool, str]:
        """
        Determine if a position should be exited.
        
        Args:
            symbol: Stock symbol
            position_data: Current position information
            
        Returns:
            Tuple of (should_exit: bool, reason: str)
        """
        pass
    
    async def validate_signal(self, signal: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Validate a trading signal before execution.
        
        Args:
            signal: Signal dictionary
            
        Returns:
            Tuple of (is_valid: bool, reason: str)
        """
        try:
            # Check required fields
            required_fields = ['symbol', 'side', 'order_type', 'entry_price']
            for field in required_fields:
                if field not in signal:
                    return False, f"Missing required field: {field}"
            
            # Validate symbol format
            symbol = signal['symbol']
            if not isinstance(symbol, str) or len(symbol) < 1 or len(symbol) > 5:
                return False, f"Invalid symbol format: {symbol}"
            
            # Validate side
            if signal['side'] not in ['BUY', 'SELL']:
                return False, f"Invalid side: {signal['side']}"
            
            # Validate order type
            if signal['order_type'] not in ['MARKET', 'LIMIT', 'STOP', 'STOP_LIMIT']:
                return False, f"Invalid order type: {signal['order_type']}"
            
            # Validate price
            entry_price = signal['entry_price']
            if not isinstance(entry_price, (int, float)) or entry_price <= 0:
                return False, f"Invalid entry price: {entry_price}"
            
            # Check minimum price threshold
            min_price = self.config.get('min_price', 1.0)
            max_price = self.config.get('max_price', 1000.0)
            if not (min_price <= entry_price <= max_price):
                return False, f"Price {entry_price} outside allowed range [{min_price}, {max_price}]"
            
            # Validate confidence if provided
            if 'confidence' in signal:
                confidence = signal['confidence']
                if not isinstance(confidence, (int, float)) or not (0.0 <= confidence <= 1.0):
                    return False, f"Invalid confidence: {confidence}"
            
            # Check for duplicate signals (same symbol within time window)
            if await self._is_duplicate_signal(signal):
                return False, "Duplicate signal within time window"
            
            # Strategy-specific validation
            strategy_validation = await self._strategy_specific_validation(signal)
            if not strategy_validation[0]:
                return False, strategy_validation[1]
            
            return True, "Signal validation passed"
            
        except Exception as e:
            logger.error(f"❌ Error validating signal", error=str(e), signal=signal)
            return False, f"Validation error: {str(e)}"
    
    async def _is_duplicate_signal(self, signal: Dict[str, Any]) -> bool:
        """Check if this is a duplicate signal within the time window."""
        symbol = signal['symbol']
        current_time = datetime.now()
        time_window = timedelta(minutes=self.config.get('signal_cooldown_minutes', 15))
        
        # Check recent signals for the same symbol
        for historical_signal in reversed(self.signal_history[-50:]):  # Check last 50 signals
            if (historical_signal['symbol'] == symbol and 
                historical_signal['side'] == signal['side'] and
                current_time - historical_signal['timestamp'] < time_window):
                return True
        
        return False
    
    async def _strategy_specific_validation(self, signal: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Strategy-specific validation logic.
        Can be overridden by individual strategies.
        """
        return True, "No additional validation required"
    
    async def record_signal(self, signal: Dict[str, Any]) -> None:
        """Record a signal in the history for analysis."""
        signal_record = {
            **signal,
            'timestamp': datetime.now(),
            'strategy': self.name
        }
        
        self.signal_history.append(signal_record)
        self.performance_metrics['total_signals'] += 1
        
        # Keep only last 1000 signals to manage memory
        if len(self.signal_history) > 1000:
            self.signal_history = self.signal_history[-1000:]
        
        logger.debug(f"📝 Signal recorded", strategy=self.name, symbol=signal['symbol'])
    
    async def update_position(self, symbol: str, position_data: Dict) -> None:
        """Update active position data."""
        self.active_positions[symbol] = {
            **position_data,
            'last_updated': datetime.now()
        }
    
    async def close_position(self, symbol: str, reason: str = "Strategy exit") -> None:
        """Mark a position as closed."""
        if symbol in self.active_positions:
            position = self.active_positions.pop(symbol)
            
            # Calculate P&L if possible
            if 'entry_price' in position and 'exit_price' in position:
                pnl = self._calculate_position_pnl(position)
                self.performance_metrics['total_pnl'] += pnl
                
                if pnl > 0:
                    self.performance_metrics['successful_trades'] += 1
                else:
                    self.performance_metrics['failed_trades'] += 1
            
            logger.info(
                f"📊 Position closed",
                strategy=self.name,
                symbol=symbol,
                reason=reason
            )
    
    def _calculate_position_pnl(self, position: Dict) -> float:
        """Calculate P&L for a position."""
        try:
            entry_price = position['entry_price']
            exit_price = position['exit_price']
            quantity = position['quantity']
            side = position['side']
            
            if side == 'BUY':
                return (exit_price - entry_price) * quantity
            else:  # SELL (short position)
                return (entry_price - exit_price) * quantity
                
        except KeyError as e:
            logger.error(f"❌ Missing data for P&L calculation", error=str(e))
            return 0.0
    
    async def get_market_data(self, symbols: List[str]) -> Dict[str, Any]:
        """Get market data for symbols."""
        try:
            return await self.schwab_client.get_quotes(symbols)
        except Exception as e:
            logger.error(f"❌ Error getting market data", error=str(e))
            return {}
    
    async def calculate_technical_indicators(
        self,
        symbol: str,
        period: int = 20
    ) -> Dict[str, float]:
        """
        Calculate common technical indicators.
        This is a simplified version - in production, you'd use a proper data provider.
        """
        try:
            # Get historical data (simplified - would need proper implementation)
            # For now, return mock indicators
            return {
                'rsi': 50.0,
                'sma_20': 100.0,
                'sma_50': 98.0,
                'bollinger_upper': 105.0,
                'bollinger_lower': 95.0,
                'macd': 0.5,
                'volume_ratio': 1.2
            }
        except Exception as e:
            logger.error(f"❌ Error calculating indicators", symbol=symbol, error=str(e))
            return {}
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get strategy performance metrics."""
        total_trades = self.performance_metrics['successful_trades'] + self.performance_metrics['failed_trades']
        
        if total_trades > 0:
            self.performance_metrics['win_rate'] = (
                self.performance_metrics['successful_trades'] / total_trades
            ) * 100
            
            self.performance_metrics['avg_return'] = (
                self.performance_metrics['total_pnl'] / total_trades
            )
        
        return {
            **self.performance_metrics,
            'active_positions': len(self.active_positions),
            'strategy_name': self.name,
            'is_enabled': self.is_enabled
        }
    
    async def reset_performance_metrics(self) -> None:
        """Reset performance tracking metrics."""
        self.performance_metrics = {
            'total_signals': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'avg_return': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0
        }
        
        logger.info(f"🔄 Performance metrics reset", strategy=self.name)
    
    def __str__(self) -> str:
        """String representation of the strategy."""
        return f"{self.name}(enabled={self.is_enabled}, positions={len(self.active_positions)})"
