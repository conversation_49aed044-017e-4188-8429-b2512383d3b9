# Core Dependencies
requests>=2.31.0
pandas>=2.0.3
numpy>=1.24.3
python-telegram-bot>=20.3
schedule>=1.2.0
python-dotenv>=1.0.0
pydantic>=2.0.0
fastapi>=0.100.0
uvicorn>=0.23.0

# Database
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.7
alembic>=1.11.1

# Analysis & Trading
yfinance>=0.2.18
ta-lib>=0.4.25
scikit-learn>=1.3.0
matplotlib>=3.7.2
seaborn>=0.12.2

# Authentication & Security
cryptography>=41.0.3
PyJWT>=2.8.0
bcrypt>=4.0.1

# Async & Concurrency
aiohttp>=3.8.5
asyncio-throttle>=1.0.2
tenacity>=8.2.2

# Monitoring & Logging
structlog>=23.1.0
prometheus-client>=0.17.1
sentry-sdk>=1.29.2

# Development & Testing
pytest>=7.4.0
pytest-asyncio>=0.21.1
pytest-cov>=4.1.0
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.1

# Cloud Deployment
gunicorn>=21.2.0
boto3>=1.28.25
google-cloud-storage>=2.10.0
azure-storage-blob>=12.17.0

# Web Framework (for dashboard)
jinja2>=3.1.2
websockets>=11.0.3
