"""
Risk Manager
Comprehensive risk management with position sizing, portfolio heat monitoring, and emergency controls.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import structlog
import numpy as np
from .position_sizer import PositionSizer

logger = structlog.get_logger(__name__)


class RiskManager:
    """
    Comprehensive risk management system with multiple layers of protection.
    
    Features:
    - Position sizing based on risk percentage
    - Portfolio heat monitoring
    - Correlation analysis
    - Sector concentration limits
    - Emergency exit protocols
    - Real-time risk metrics
    """
    
    def __init__(self, config: Dict[str, Any], schwab_client):
        """Initialize risk manager."""
        self.config = config
        self.schwab_client = schwab_client
        
        # Risk parameters
        self.max_portfolio_risk = config.get('max_portfolio_risk', 0.02)  # 2%
        self.max_position_size = config.get('max_position_size', 0.05)    # 5%
        self.stop_loss_pct = config.get('stop_loss_pct', 0.02)           # 2%
        self.max_drawdown = config.get('max_drawdown', 0.15)             # 15%
        self.correlation_limit = config.get('correlation_limit', 0.7)     # 70%
        self.sector_limit = config.get('sector_concentration_limit', 0.3) # 30%
        
        # Position sizer
        self.position_sizer = PositionSizer(config)
        
        # Risk tracking
        self.portfolio_heat = 0.0
        self.current_drawdown = 0.0
        self.risk_metrics = {
            'total_risk': 0.0,
            'position_count': 0,
            'sector_exposure': {},
            'correlation_warnings': [],
            'last_updated': datetime.now()
        }
        
        # Emergency controls
        self.emergency_mode = False
        self.risk_violations = []
        
        logger.info(
            "✅ Risk manager initialized",
            max_portfolio_risk=self.max_portfolio_risk,
            max_position_size=self.max_position_size,
            max_drawdown=self.max_drawdown
        )
    
    async def validate_trade(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Comprehensive trade validation before execution.
        
        Args:
            signal: Trading signal dictionary
            
        Returns:
            Dict with 'approved' boolean and 'reason' string
        """
        try:
            symbol = signal['symbol']
            side = signal['side']
            entry_price = signal['entry_price']
            
            # Emergency mode check
            if self.emergency_mode:
                return {
                    'approved': False,
                    'reason': 'Emergency mode active - all trades blocked'
                }
            
            # Get current account info
            account_info = await self.schwab_client.get_account_info()
            if not account_info:
                return {
                    'approved': False,
                    'reason': 'Unable to retrieve account information'
                }
            
            account_value = account_info.get('totalValue', 0)
            buying_power = account_info.get('buyingPower', 0)
            
            # Calculate position size
            position_size = await self.calculate_position_size(
                symbol, entry_price, signal.get('stop_loss')
            )
            
            if position_size <= 0:
                return {
                    'approved': False,
                    'reason': 'Invalid position size calculated'
                }
            
            # Position value check
            position_value = position_size * entry_price
            
            # 1. Buying power check
            if position_value > buying_power:
                return {
                    'approved': False,
                    'reason': f'Insufficient buying power: ${buying_power:,.2f} < ${position_value:,.2f}'
                }
            
            # 2. Position size limit check
            position_pct = position_value / account_value
            if position_pct > self.max_position_size:
                return {
                    'approved': False,
                    'reason': f'Position size too large: {position_pct:.1%} > {self.max_position_size:.1%}'
                }
            
            # 3. Portfolio heat check
            await self._update_portfolio_heat()
            if self.portfolio_heat > 0.8:  # 80% heat threshold
                return {
                    'approved': False,
                    'reason': f'Portfolio heat too high: {self.portfolio_heat:.1%}'
                }
            
            # 4. Correlation check
            correlation_risk = await self._check_correlation_risk(symbol)
            if correlation_risk['high_correlation']:
                return {
                    'approved': False,
                    'reason': f'High correlation risk: {correlation_risk["reason"]}'
                }
            
            # 5. Sector concentration check
            sector_risk = await self._check_sector_concentration(symbol, position_value, account_value)
            if sector_risk['over_limit']:
                return {
                    'approved': False,
                    'reason': f'Sector concentration limit exceeded: {sector_risk["reason"]}'
                }
            
            # 6. Drawdown check
            if self.current_drawdown > self.max_drawdown:
                return {
                    'approved': False,
                    'reason': f'Maximum drawdown exceeded: {self.current_drawdown:.1%}'
                }
            
            # 7. Risk per trade check
            stop_loss = signal.get('stop_loss')
            if stop_loss:
                risk_per_trade = abs(entry_price - stop_loss) * position_size
                max_risk = account_value * self.max_portfolio_risk
                
                if risk_per_trade > max_risk:
                    return {
                        'approved': False,
                        'reason': f'Risk per trade too high: ${risk_per_trade:,.2f} > ${max_risk:,.2f}'
                    }
            
            # All checks passed
            logger.info(
                "✅ Trade approved by risk manager",
                symbol=symbol,
                position_size=position_size,
                position_value=position_value,
                portfolio_heat=self.portfolio_heat
            )
            
            return {
                'approved': True,
                'reason': 'All risk checks passed',
                'position_size': position_size,
                'position_value': position_value,
                'risk_metrics': self.risk_metrics
            }
            
        except Exception as e:
            logger.error("❌ Error in trade validation", error=str(e), exc_info=True)
            return {
                'approved': False,
                'reason': f'Risk validation error: {str(e)}'
            }
    
    async def calculate_position_size(
        self,
        symbol: str,
        entry_price: float,
        stop_loss: Optional[float] = None
    ) -> int:
        """Calculate optimal position size based on risk parameters."""
        try:
            # Get account information
            account_info = await self.schwab_client.get_account_info()
            if not account_info:
                logger.error("❌ Unable to get account info for position sizing")
                return 0
            
            account_value = account_info.get('totalValue', 0)
            
            # Use position sizer
            position_size = await self.position_sizer.calculate_size(
                account_value=account_value,
                entry_price=entry_price,
                stop_loss=stop_loss,
                risk_per_trade=self.max_portfolio_risk
            )
            
            logger.debug(
                "📏 Position size calculated",
                symbol=symbol,
                entry_price=entry_price,
                stop_loss=stop_loss,
                position_size=position_size
            )
            
            return position_size
            
        except Exception as e:
            logger.error("❌ Error calculating position size", error=str(e))
            return 0
    
    async def _update_portfolio_heat(self) -> None:
        """Update portfolio heat metric."""
        try:
            positions = await self.schwab_client.get_positions()
            if not positions:
                self.portfolio_heat = 0.0
                return
            
            total_risk = 0.0
            total_value = 0.0
            
            for position in positions:
                position_value = abs(position.get('marketValue', 0))
                total_value += position_value
                
                # Estimate risk based on position volatility (simplified)
                # In production, you'd use actual volatility metrics
                estimated_risk = position_value * 0.02  # Assume 2% daily risk
                total_risk += estimated_risk
            
            if total_value > 0:
                self.portfolio_heat = total_risk / total_value
            else:
                self.portfolio_heat = 0.0
            
            self.risk_metrics['total_risk'] = total_risk
            self.risk_metrics['position_count'] = len(positions)
            self.risk_metrics['last_updated'] = datetime.now()
            
        except Exception as e:
            logger.error("❌ Error updating portfolio heat", error=str(e))
    
    async def _check_correlation_risk(self, symbol: str) -> Dict[str, Any]:
        """Check correlation risk with existing positions."""
        try:
            positions = await self.schwab_client.get_positions()
            if not positions:
                return {'high_correlation': False, 'reason': 'No existing positions'}
            
            # Simplified correlation check
            # In production, you'd use actual correlation data
            existing_symbols = [pos.get('instrument', {}).get('symbol', '') for pos in positions]
            
            # Check for sector correlation (simplified)
            tech_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META']
            finance_stocks = ['JPM', 'BAC', 'WFC', 'GS', 'MS', 'C']
            
            if symbol in tech_stocks:
                tech_positions = [s for s in existing_symbols if s in tech_stocks]
                if len(tech_positions) >= 3:
                    return {
                        'high_correlation': True,
                        'reason': f'Too many tech positions: {tech_positions}'
                    }
            
            if symbol in finance_stocks:
                finance_positions = [s for s in existing_symbols if s in finance_stocks]
                if len(finance_positions) >= 2:
                    return {
                        'high_correlation': True,
                        'reason': f'Too many finance positions: {finance_positions}'
                    }
            
            return {'high_correlation': False, 'reason': 'Correlation within limits'}
            
        except Exception as e:
            logger.error("❌ Error checking correlation risk", error=str(e))
            return {'high_correlation': False, 'reason': f'Error: {str(e)}'}
    
    async def _check_sector_concentration(
        self,
        symbol: str,
        position_value: float,
        account_value: float
    ) -> Dict[str, Any]:
        """Check sector concentration limits."""
        try:
            # Simplified sector mapping
            sector_map = {
                'AAPL': 'Technology', 'MSFT': 'Technology', 'GOOGL': 'Technology',
                'AMZN': 'Consumer Discretionary', 'TSLA': 'Consumer Discretionary',
                'NVDA': 'Technology', 'META': 'Technology',
                'JPM': 'Financials', 'BAC': 'Financials', 'WFC': 'Financials'
            }
            
            symbol_sector = sector_map.get(symbol, 'Other')
            
            # Get current positions
            positions = await self.schwab_client.get_positions()
            sector_exposure = {}
            
            for position in positions:
                pos_symbol = position.get('instrument', {}).get('symbol', '')
                pos_value = abs(position.get('marketValue', 0))
                pos_sector = sector_map.get(pos_symbol, 'Other')
                
                sector_exposure[pos_sector] = sector_exposure.get(pos_sector, 0) + pos_value
            
            # Add new position
            sector_exposure[symbol_sector] = sector_exposure.get(symbol_sector, 0) + position_value
            
            # Check sector limit
            sector_pct = sector_exposure[symbol_sector] / account_value
            if sector_pct > self.sector_limit:
                return {
                    'over_limit': True,
                    'reason': f'{symbol_sector} sector exposure: {sector_pct:.1%} > {self.sector_limit:.1%}'
                }
            
            # Update risk metrics
            self.risk_metrics['sector_exposure'] = {
                sector: (value / account_value) for sector, value in sector_exposure.items()
            }
            
            return {'over_limit': False, 'reason': 'Sector concentration within limits'}
            
        except Exception as e:
            logger.error("❌ Error checking sector concentration", error=str(e))
            return {'over_limit': False, 'reason': f'Error: {str(e)}'}
    
    async def monitor_positions(self) -> List[Dict[str, Any]]:
        """Monitor existing positions for risk violations."""
        risk_alerts = []
        
        try:
            positions = await self.schwab_client.get_positions()
            if not positions:
                return risk_alerts
            
            for position in positions:
                symbol = position.get('instrument', {}).get('symbol', '')
                current_value = position.get('marketValue', 0)
                unrealized_pnl = position.get('unrealizedPnL', 0)
                unrealized_pnl_pct = position.get('unrealizedPnLPercent', 0)
                
                # Check for large losses
                if unrealized_pnl_pct < -0.05:  # 5% loss
                    risk_alerts.append({
                        'type': 'position_loss',
                        'symbol': symbol,
                        'loss_pct': unrealized_pnl_pct,
                        'loss_amount': unrealized_pnl,
                        'priority': 'high' if unrealized_pnl_pct < -0.10 else 'medium'
                    })
                
                # Check for oversized positions
                account_info = await self.schwab_client.get_account_info()
                if account_info:
                    account_value = account_info.get('totalValue', 1)
                    position_pct = abs(current_value) / account_value
                    
                    if position_pct > self.max_position_size:
                        risk_alerts.append({
                            'type': 'oversized_position',
                            'symbol': symbol,
                            'position_pct': position_pct,
                            'limit': self.max_position_size,
                            'priority': 'high'
                        })
            
            return risk_alerts
            
        except Exception as e:
            logger.error("❌ Error monitoring positions", error=str(e))
            return []
    
    async def emergency_exit_all(self, reason: str = "Emergency exit") -> Dict[str, Any]:
        """Emergency exit all positions."""
        try:
            logger.warning(f"🚨 Emergency exit triggered: {reason}")
            self.emergency_mode = True
            
            positions = await self.schwab_client.get_positions()
            if not positions:
                return {'success': True, 'message': 'No positions to exit'}
            
            exit_results = []
            
            for position in positions:
                symbol = position.get('instrument', {}).get('symbol', '')
                quantity = position.get('longQuantity', 0) - position.get('shortQuantity', 0)
                
                if quantity != 0:
                    # Place market order to exit
                    side = 'SELL' if quantity > 0 else 'BUY'
                    result = await self.schwab_client.place_order(
                        symbol=symbol,
                        quantity=abs(quantity),
                        order_type='MARKET',
                        side=side
                    )
                    
                    exit_results.append({
                        'symbol': symbol,
                        'quantity': quantity,
                        'side': side,
                        'success': result.get('success', False),
                        'order_id': result.get('order_id')
                    })
            
            return {
                'success': True,
                'message': f'Emergency exit initiated for {len(exit_results)} positions',
                'results': exit_results
            }
            
        except Exception as e:
            logger.error("❌ Error in emergency exit", error=str(e))
            return {'success': False, 'error': str(e)}
    
    def get_risk_metrics(self) -> Dict[str, Any]:
        """Get current risk metrics."""
        return {
            **self.risk_metrics,
            'portfolio_heat': self.portfolio_heat,
            'current_drawdown': self.current_drawdown,
            'emergency_mode': self.emergency_mode,
            'risk_violations': len(self.risk_violations),
            'max_portfolio_risk': self.max_portfolio_risk,
            'max_position_size': self.max_position_size
        }
