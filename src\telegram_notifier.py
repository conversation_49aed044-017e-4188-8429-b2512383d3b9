"""
Telegram Notifier
Handles all Telegram bot communications and alert formatting.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
import structlog
from telegram import Bo<PERSON>
from telegram.error import TelegramError, RetryAfter
from tenacity import retry, stop_after_attempt, wait_exponential

logger = structlog.get_logger(__name__)


class TelegramNotifier:
    """
    Telegram notification service with rich formatting, priority levels,
    and comprehensive error handling.
    """
    
    def __init__(self, bot_token: str, chat_id: str):
        """Initialize Telegram notifier."""
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.bot: Optional[Bot] = None
        
        # Message formatting templates
        self.templates = {
            'trade': self._format_trade_message,
            'portfolio': self._format_portfolio_message,
            'risk': self._format_risk_message,
            'system': self._format_system_message,
            'market': self._format_market_message
        }
        
        # Priority emojis
        self.priority_emojis = {
            'low': '🔵',
            'normal': '🟢',
            'high': '🟡',
            'critical': '🔴'
        }
        
        # Message statistics
        self.message_stats = {
            'total_sent': 0,
            'successful_sent': 0,
            'failed_sent': 0,
            'rate_limited': 0
        }
    
    async def initialize(self) -> None:
        """Initialize the Telegram bot."""
        try:
            self.bot = Bot(token=self.bot_token)
            
            # Test connection
            bot_info = await self.bot.get_me()
            logger.info(
                "✅ Telegram bot initialized successfully",
                bot_name=bot_info.first_name,
                bot_username=bot_info.username
            )
            
            # Send initialization message
            await self.send_system_alert(
                "initialization",
                "🤖 Telegram notifier initialized successfully!\n"
                f"Bot: @{bot_info.username}\n"
                f"Chat ID: {self.chat_id}\n"
                f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            
        except Exception as e:
            logger.error("❌ Failed to initialize Telegram bot", error=str(e), exc_info=True)
            raise
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _send_message(
        self,
        message: str,
        priority: str = 'normal',
        parse_mode: str = 'HTML',
        disable_web_page_preview: bool = True
    ) -> Optional[int]:
        """Send message with retry logic and rate limiting handling."""
        if not self.bot:
            logger.error("❌ Telegram bot not initialized")
            return None
        
        self.message_stats['total_sent'] += 1
        
        try:
            # Add priority indicator
            priority_emoji = self.priority_emojis.get(priority, '🟢')
            formatted_message = f"{priority_emoji} {message}"
            
            # Send message
            sent_message = await self.bot.send_message(
                chat_id=self.chat_id,
                text=formatted_message,
                parse_mode=parse_mode,
                disable_web_page_preview=disable_web_page_preview
            )
            
            self.message_stats['successful_sent'] += 1
            logger.debug("📤 Message sent successfully", message_id=sent_message.message_id)
            
            return sent_message.message_id
            
        except RetryAfter as e:
            self.message_stats['rate_limited'] += 1
            logger.warning(f"⚠️ Rate limited by Telegram, waiting {e.retry_after} seconds")
            await asyncio.sleep(e.retry_after)
            raise
            
        except TelegramError as e:
            self.message_stats['failed_sent'] += 1
            logger.error("❌ Telegram error", error=str(e))
            raise
            
        except Exception as e:
            self.message_stats['failed_sent'] += 1
            logger.error("❌ Unexpected error sending message", error=str(e))
            raise
    
    async def send_trade_notification(self, trade_data: Dict[str, Any]) -> None:
        """Send formatted trade execution notification."""
        message = self._format_trade_message(trade_data)
        priority = 'high' if trade_data.get('side') == 'SELL' else 'normal'
        await self._send_message(message, priority=priority)
    
    async def send_portfolio_summary(self, portfolio_data: Dict[str, Any]) -> None:
        """Send portfolio summary notification."""
        message = self._format_portfolio_message(portfolio_data)
        priority = 'normal'
        
        # Increase priority for significant changes
        if portfolio_data.get('day_pnl_pct', 0) > 5 or portfolio_data.get('day_pnl_pct', 0) < -3:
            priority = 'high'
        
        await self._send_message(message, priority=priority)
    
    async def send_risk_alert(self, risk_data: Dict[str, Any]) -> None:
        """Send risk management alert."""
        message = self._format_risk_message(risk_data)
        priority = risk_data.get('priority', 'high')
        await self._send_message(message, priority=priority)
    
    async def send_system_alert(self, alert_type: str, message: str, priority: str = 'normal') -> None:
        """Send system status alert."""
        formatted_message = self._format_system_message(alert_type, message)
        await self._send_message(formatted_message, priority=priority)
    
    async def send_market_analysis(self, analysis_data: Dict[str, Any]) -> None:
        """Send market analysis notification."""
        message = self._format_market_message(analysis_data)
        await self._send_message(message, priority='low')
    
    def _format_trade_message(self, trade_data: Dict[str, Any]) -> str:
        """Format trade execution message."""
        action_emoji = "🟢" if trade_data['action'] == 'BUY' else "🔴"
        
        message = f"""
{action_emoji} <b>TRADE EXECUTED</b>

📊 <b>Symbol:</b> {trade_data['symbol']}
🎯 <b>Action:</b> {trade_data['action']}
📈 <b>Quantity:</b> {trade_data['quantity']:,}
💰 <b>Price:</b> ${trade_data['price']:.2f}
💵 <b>Total:</b> ${trade_data['quantity'] * trade_data['price']:,.2f}
⏰ <b>Time:</b> {datetime.now().strftime('%H:%M:%S EST')}
🧠 <b>Strategy:</b> {trade_data.get('strategy', 'Manual')}
🆔 <b>Order ID:</b> {trade_data.get('order_id', 'N/A')}
        """.strip()
        
        return message
    
    def _format_portfolio_message(self, portfolio_data: Dict[str, Any]) -> str:
        """Format portfolio summary message."""
        pnl_emoji = "📈" if portfolio_data.get('day_pnl', 0) >= 0 else "📉"
        
        message = f"""
💼 <b>PORTFOLIO UPDATE</b>

💰 <b>Total Value:</b> ${portfolio_data.get('total_value', 0):,.2f}
{pnl_emoji} <b>Day P&L:</b> ${portfolio_data.get('day_pnl', 0):,.2f} ({portfolio_data.get('day_pnl_pct', 0):+.2f}%)
🏆 <b>Top Performer:</b> {portfolio_data.get('top_performer', 'N/A')}
📊 <b>Active Positions:</b> {portfolio_data.get('position_count', 0)}
💵 <b>Cash Available:</b> ${portfolio_data.get('cash_available', 0):,.2f}
📈 <b>Buying Power:</b> ${portfolio_data.get('buying_power', 0):,.2f}
⏰ <b>Updated:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """.strip()
        
        return message
    
    def _format_risk_message(self, risk_data: Dict[str, Any]) -> str:
        """Format risk management alert."""
        risk_level = risk_data.get('level', 'MEDIUM')
        risk_emoji = {
            'LOW': '🟢',
            'MEDIUM': '🟡', 
            'HIGH': '🟠',
            'CRITICAL': '🔴'
        }.get(risk_level, '🟡')
        
        message = f"""
{risk_emoji} <b>RISK ALERT - {risk_level}</b>

⚠️ <b>Alert Type:</b> {risk_data.get('type', 'Unknown')}
📊 <b>Current Risk:</b> {risk_data.get('current_risk', 0):.1f}%
🎯 <b>Risk Limit:</b> {risk_data.get('risk_limit', 0):.1f}%
📈 <b>Portfolio Heat:</b> {risk_data.get('portfolio_heat', 0):.1f}%
🔍 <b>Details:</b> {risk_data.get('details', 'No details available')}
🎯 <b>Action Required:</b> {risk_data.get('action_required', 'Monitor closely')}
⏰ <b>Time:</b> {datetime.now().strftime('%H:%M:%S EST')}
        """.strip()
        
        return message
    
    def _format_system_message(self, alert_type: str, message: str) -> str:
        """Format system status message."""
        type_emojis = {
            'startup': '🚀',
            'shutdown': '🔄',
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️',
            'market_open': '🔔',
            'market_close': '🔕',
            'initialization': '🤖'
        }
        
        emoji = type_emojis.get(alert_type, 'ℹ️')
        
        formatted_message = f"""
{emoji} <b>SYSTEM {alert_type.upper()}</b>

{message}

⏰ <b>Timestamp:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S EST')}
        """.strip()
        
        return formatted_message
    
    def _format_market_message(self, analysis_data: Dict[str, Any]) -> str:
        """Format market analysis message."""
        sentiment_emoji = {
            'bullish': '🐂',
            'bearish': '🐻',
            'neutral': '➡️'
        }.get(analysis_data.get('sentiment', 'neutral'), '➡️')
        
        message = f"""
📊 <b>MARKET ANALYSIS</b>

{sentiment_emoji} <b>Sentiment:</b> {analysis_data.get('sentiment', 'Neutral').title()}
📈 <b>SPY:</b> ${analysis_data.get('spy_price', 0):.2f} ({analysis_data.get('spy_change', 0):+.2f}%)
📊 <b>VIX:</b> {analysis_data.get('vix_level', 0):.2f}
🎯 <b>Key Levels:</b> {analysis_data.get('key_levels', 'N/A')}
📰 <b>Market News:</b> {analysis_data.get('news_summary', 'No significant news')}
⏰ <b>Analysis Time:</b> {datetime.now().strftime('%H:%M:%S EST')}
        """.strip()
        
        return message
    
    async def send_custom_message(self, message: str, priority: str = 'normal') -> None:
        """Send a custom formatted message."""
        await self._send_message(message, priority=priority)
    
    def get_message_stats(self) -> Dict[str, int]:
        """Get message sending statistics."""
        return self.message_stats.copy()
    
    async def close(self) -> None:
        """Close the Telegram bot session."""
        if self.bot:
            # Send shutdown notification
            await self.send_system_alert(
                "shutdown",
                "🔄 Telegram notifier shutting down...\n"
                f"📊 Session Stats:\n"
                f"• Messages Sent: {self.message_stats['successful_sent']}\n"
                f"• Success Rate: {self._calculate_success_rate():.1f}%"
            )
        
        logger.info("✅ Telegram notifier closed")
    
    def _calculate_success_rate(self) -> float:
        """Calculate message success rate."""
        if self.message_stats['total_sent'] == 0:
            return 0.0
        
        return (self.message_stats['successful_sent'] / self.message_stats['total_sent']) * 100
