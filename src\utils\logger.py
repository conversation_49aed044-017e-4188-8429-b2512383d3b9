"""
Structured Logging Configuration
Advanced logging setup with structured logs, multiple outputs, and monitoring integration.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import os
import sys
import logging
import logging.handlers
from pathlib import Path
from typing import Dict, Any, Optional
import structlog
from structlog.stdlib import LoggerFactory
import json
from datetime import datetime


def setup_logging(
    log_level: str = None,
    log_format: str = None,
    log_file: str = None,
    enable_sentry: bool = True,
    enable_json: bool = True
) -> structlog.BoundLogger:
    """
    Setup comprehensive structured logging with multiple outputs.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: Log format ('json' or 'console')
        log_file: Log file path
        enable_sentry: Enable Sentry error tracking
        enable_json: Enable JSON formatting
        
    Returns:
        Configured structlog logger
    """
    
    # Get configuration from environment or use defaults
    log_level = log_level or os.getenv('LOG_LEVEL', 'INFO')
    log_format = log_format or os.getenv('LOG_FORMAT', 'json')
    log_file = log_file or os.getenv('LOG_FILE', 'logs/trading_bot.log')
    
    # Create logs directory if it doesn't exist
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, log_level.upper())
    )
    
    # Setup processors based on format
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="ISO"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        add_trading_context,
        add_performance_metrics,
    ]
    
    # Add JSON or console formatting
    if enable_json and log_format.lower() == 'json':
        processors.append(structlog.processors.JSONRenderer())
    else:
        processors.extend([
            structlog.dev.ConsoleRenderer(colors=True),
            structlog.processors.add_log_level,
        ])
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=LoggerFactory(),
        context_class=dict,
        cache_logger_on_first_use=True,
    )
    
    # Setup file handler with rotation
    setup_file_logging(log_file, log_level)
    
    # Setup Sentry for error tracking
    if enable_sentry:
        setup_sentry_logging()
    
    # Setup performance logging
    setup_performance_logging()
    
    # Get logger instance
    logger = structlog.get_logger("trading_bot")
    
    logger.info(
        "🚀 Logging system initialized",
        log_level=log_level,
        log_format=log_format,
        log_file=log_file,
        sentry_enabled=enable_sentry
    )
    
    return logger


def setup_file_logging(log_file: str, log_level: str) -> None:
    """Setup rotating file handler for persistent logging."""
    
    # Create rotating file handler
    file_handler = logging.handlers.RotatingFileHandler(
        filename=log_file,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    
    # Set formatter for file output
    file_formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(file_formatter)
    file_handler.setLevel(getattr(logging, log_level.upper()))
    
    # Add to root logger
    root_logger = logging.getLogger()
    root_logger.addHandler(file_handler)


def setup_sentry_logging() -> None:
    """Setup Sentry for error tracking and monitoring."""
    sentry_dsn = os.getenv('SENTRY_DSN')
    
    if not sentry_dsn:
        return
    
    try:
        import sentry_sdk
        from sentry_sdk.integrations.logging import LoggingIntegration
        from sentry_sdk.integrations.asyncio import AsyncioIntegration
        
        # Configure Sentry integrations
        sentry_logging = LoggingIntegration(
            level=logging.INFO,        # Capture info and above as breadcrumbs
            event_level=logging.ERROR  # Send errors as events
        )
        
        sentry_sdk.init(
            dsn=sentry_dsn,
            integrations=[
                sentry_logging,
                AsyncioIntegration(transaction_style="task_name")
            ],
            traces_sample_rate=0.1,  # 10% of transactions for performance monitoring
            environment=os.getenv('ENVIRONMENT', 'development'),
            release=os.getenv('VERSION', '1.0.0'),
            before_send=filter_sensitive_data,
            attach_stacktrace=True,
            send_default_pii=False
        )
        
        # Add custom tags
        sentry_sdk.set_tag("component", "trading_bot")
        sentry_sdk.set_tag("service", "schwab_telegram_bot")
        
        print("✅ Sentry error tracking initialized")
        
    except ImportError:
        print("⚠️ Sentry SDK not installed, skipping error tracking")
    except Exception as e:
        print(f"❌ Failed to initialize Sentry: {e}")


def setup_performance_logging() -> None:
    """Setup performance monitoring and metrics logging."""
    
    # Create performance logger
    perf_logger = logging.getLogger('performance')
    perf_logger.setLevel(logging.INFO)
    
    # Performance log file
    perf_file = 'logs/performance.log'
    Path(perf_file).parent.mkdir(parents=True, exist_ok=True)
    
    # Performance file handler
    perf_handler = logging.handlers.RotatingFileHandler(
        filename=perf_file,
        maxBytes=5 * 1024 * 1024,  # 5MB
        backupCount=3
    )
    
    # JSON formatter for performance metrics
    perf_formatter = logging.Formatter('%(message)s')
    perf_handler.setFormatter(perf_formatter)
    perf_logger.addHandler(perf_handler)
    
    # Prevent propagation to root logger
    perf_logger.propagate = False


def add_trading_context(logger, method_name, event_dict):
    """Add trading-specific context to log entries."""
    
    # Add timestamp if not present
    if 'timestamp' not in event_dict:
        event_dict['timestamp'] = datetime.utcnow().isoformat()
    
    # Add service information
    event_dict['service'] = 'schwab_telegram_trading_bot'
    event_dict['version'] = '1.0.0'
    
    # Add environment context
    event_dict['environment'] = os.getenv('ENVIRONMENT', 'development')
    
    # Add process information
    event_dict['pid'] = os.getpid()
    
    return event_dict


def add_performance_metrics(logger, method_name, event_dict):
    """Add performance metrics to log entries."""
    
    # Add memory usage if available
    try:
        import psutil
        process = psutil.Process()
        event_dict['memory_mb'] = round(process.memory_info().rss / 1024 / 1024, 2)
        event_dict['cpu_percent'] = process.cpu_percent()
    except ImportError:
        pass
    except Exception:
        pass
    
    return event_dict


def filter_sensitive_data(event, hint):
    """Filter sensitive data from Sentry events."""
    
    # Remove sensitive keys
    sensitive_keys = [
        'password', 'token', 'key', 'secret', 'auth',
        'client_secret', 'access_token', 'refresh_token',
        'bot_token', 'api_key'
    ]
    
    def remove_sensitive(obj):
        if isinstance(obj, dict):
            return {
                k: '[REDACTED]' if any(sensitive in k.lower() for sensitive in sensitive_keys)
                else remove_sensitive(v)
                for k, v in obj.items()
            }
        elif isinstance(obj, list):
            return [remove_sensitive(item) for item in obj]
        else:
            return obj
    
    # Filter event data
    if 'extra' in event:
        event['extra'] = remove_sensitive(event['extra'])
    
    if 'contexts' in event:
        event['contexts'] = remove_sensitive(event['contexts'])
    
    return event


class TradingLogger:
    """Specialized logger for trading operations with structured metrics."""
    
    def __init__(self, name: str = "trading"):
        """Initialize trading logger."""
        self.logger = structlog.get_logger(name)
        self.performance_logger = logging.getLogger('performance')
    
    def log_trade(
        self,
        symbol: str,
        side: str,
        quantity: int,
        price: float,
        strategy: str,
        order_id: str = None,
        **kwargs
    ) -> None:
        """Log trade execution with structured data."""
        
        trade_data = {
            'event_type': 'trade_execution',
            'symbol': symbol,
            'side': side,
            'quantity': quantity,
            'price': price,
            'total_value': quantity * price,
            'strategy': strategy,
            'order_id': order_id,
            'timestamp': datetime.utcnow().isoformat(),
            **kwargs
        }
        
        self.logger.info("📊 Trade executed", **trade_data)
        self.performance_logger.info(json.dumps(trade_data))
    
    def log_signal(
        self,
        symbol: str,
        signal_type: str,
        confidence: float,
        strategy: str,
        **kwargs
    ) -> None:
        """Log trading signal generation."""
        
        signal_data = {
            'event_type': 'trading_signal',
            'symbol': symbol,
            'signal_type': signal_type,
            'confidence': confidence,
            'strategy': strategy,
            'timestamp': datetime.utcnow().isoformat(),
            **kwargs
        }
        
        self.logger.info("🎯 Trading signal generated", **signal_data)
        self.performance_logger.info(json.dumps(signal_data))
    
    def log_performance(
        self,
        metric_name: str,
        value: float,
        unit: str = None,
        **kwargs
    ) -> None:
        """Log performance metrics."""
        
        perf_data = {
            'event_type': 'performance_metric',
            'metric_name': metric_name,
            'value': value,
            'unit': unit,
            'timestamp': datetime.utcnow().isoformat(),
            **kwargs
        }
        
        self.performance_logger.info(json.dumps(perf_data))
    
    def log_error(
        self,
        error_type: str,
        error_message: str,
        context: Dict[str, Any] = None,
        **kwargs
    ) -> None:
        """Log errors with context."""
        
        error_data = {
            'event_type': 'error',
            'error_type': error_type,
            'error_message': error_message,
            'context': context or {},
            'timestamp': datetime.utcnow().isoformat(),
            **kwargs
        }
        
        self.logger.error("❌ Error occurred", **error_data)


# Global trading logger instance
trading_logger = TradingLogger()
