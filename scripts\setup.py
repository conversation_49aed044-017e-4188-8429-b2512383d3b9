#!/usr/bin/env python3
"""
Setup Script for Schwab Telegram Trading Bot
Automated setup with environment validation, dependency installation, and configuration.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import os
import sys
import subprocess
import json
import shutil
from pathlib import Path
from typing import Dict, List, Optional
import argparse


class TradingBotSetup:
    """Comprehensive setup manager for the trading bot."""
    
    def __init__(self):
        """Initialize setup manager."""
        self.project_root = Path(__file__).parent.parent
        self.required_python_version = (3, 9)
        self.setup_steps = []
        
    def run_setup(self, skip_deps: bool = False, dev_mode: bool = False) -> None:
        """Run complete setup process."""
        print("🚀 Starting Schwab Telegram Trading Bot Setup")
        print("=" * 60)
        
        try:
            # Setup steps
            self.check_python_version()
            self.create_directories()
            self.setup_environment_file()
            self.setup_configuration()
            
            if not skip_deps:
                self.install_dependencies(dev_mode)
            
            self.setup_database()
            self.validate_setup()
            self.display_next_steps()
            
            print("\n✅ Setup completed successfully!")
            
        except Exception as e:
            print(f"\n❌ Setup failed: {e}")
            sys.exit(1)
    
    def check_python_version(self) -> None:
        """Check Python version compatibility."""
        print("🔍 Checking Python version...")
        
        current_version = sys.version_info[:2]
        if current_version < self.required_python_version:
            raise Exception(
                f"Python {self.required_python_version[0]}.{self.required_python_version[1]}+ "
                f"required, but {current_version[0]}.{current_version[1]} found"
            )
        
        print(f"✅ Python {current_version[0]}.{current_version[1]} - Compatible")
    
    def create_directories(self) -> None:
        """Create necessary directories."""
        print("📁 Creating project directories...")
        
        directories = [
            'logs',
            'data',
            'backups',
            'temp',
            'static',
            'templates'
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(exist_ok=True)
            print(f"  ✅ Created: {directory}/")
    
    def setup_environment_file(self) -> None:
        """Setup environment file from template."""
        print("🔧 Setting up environment file...")
        
        env_template = self.project_root / '.env.template'
        env_file = self.project_root / '.env'
        
        if not env_template.exists():
            raise Exception(".env.template not found")
        
        if not env_file.exists():
            shutil.copy(env_template, env_file)
            print("  ✅ Created .env from template")
            print("  ⚠️  Please edit .env with your actual credentials")
        else:
            print("  ℹ️  .env already exists, skipping")
    
    def setup_configuration(self) -> None:
        """Setup configuration file from template."""
        print("⚙️  Setting up configuration file...")
        
        config_template = self.project_root / 'config.json.template'
        config_file = self.project_root / 'config.json'
        
        if not config_template.exists():
            raise Exception("config.json.template not found")
        
        if not config_file.exists():
            shutil.copy(config_template, config_file)
            print("  ✅ Created config.json from template")
            print("  ⚠️  Please edit config.json with your settings")
        else:
            print("  ℹ️  config.json already exists, skipping")
    
    def install_dependencies(self, dev_mode: bool = False) -> None:
        """Install Python dependencies."""
        print("📦 Installing Python dependencies...")
        
        requirements_file = self.project_root / 'requirements.txt'
        if not requirements_file.exists():
            raise Exception("requirements.txt not found")
        
        # Install main dependencies
        try:
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)
            ], check=True, capture_output=True, text=True)
            print("  ✅ Main dependencies installed")
        except subprocess.CalledProcessError as e:
            print(f"  ❌ Failed to install dependencies: {e.stderr}")
            raise
        
        # Install development dependencies if requested
        if dev_mode:
            dev_requirements = [
                'pytest>=7.4.0',
                'pytest-asyncio>=0.21.1',
                'pytest-cov>=4.1.0',
                'black>=23.7.0',
                'flake8>=6.0.0',
                'mypy>=1.5.1',
                'pre-commit>=3.3.3'
            ]
            
            try:
                subprocess.run([
                    sys.executable, '-m', 'pip', 'install'
                ] + dev_requirements, check=True, capture_output=True, text=True)
                print("  ✅ Development dependencies installed")
            except subprocess.CalledProcessError as e:
                print(f"  ⚠️  Failed to install dev dependencies: {e.stderr}")
    
    def setup_database(self) -> None:
        """Setup database (SQLite for development)."""
        print("🗄️  Setting up database...")
        
        # Create data directory
        data_dir = self.project_root / 'data'
        data_dir.mkdir(exist_ok=True)
        
        # For now, we'll use SQLite for development
        db_file = data_dir / 'trading_bot.db'
        
        print(f"  ✅ Database will be created at: {db_file}")
        print("  ℹ️  Database tables will be created on first run")
    
    def validate_setup(self) -> None:
        """Validate the setup."""
        print("🔍 Validating setup...")
        
        # Check critical files
        critical_files = [
            '.env',
            'config.json',
            'requirements.txt',
            'main.py'
        ]
        
        for file_name in critical_files:
            file_path = self.project_root / file_name
            if not file_path.exists():
                raise Exception(f"Critical file missing: {file_name}")
        
        # Check directories
        critical_dirs = ['src', 'logs', 'data']
        for dir_name in critical_dirs:
            dir_path = self.project_root / dir_name
            if not dir_path.exists():
                raise Exception(f"Critical directory missing: {dir_name}")
        
        print("  ✅ All critical files and directories present")
        
        # Try importing main modules
        try:
            sys.path.insert(0, str(self.project_root / 'src'))
            import trading_bot
            print("  ✅ Main modules can be imported")
        except ImportError as e:
            print(f"  ⚠️  Import warning: {e}")
    
    def display_next_steps(self) -> None:
        """Display next steps for the user."""
        print("\n" + "=" * 60)
        print("🎯 NEXT STEPS")
        print("=" * 60)
        
        steps = [
            "1. Edit .env file with your actual API credentials:",
            "   - SCHWAB_CLIENT_ID",
            "   - SCHWAB_CLIENT_SECRET", 
            "   - TELEGRAM_BOT_TOKEN",
            "   - TELEGRAM_CHAT_ID",
            "",
            "2. Edit config.json with your trading preferences:",
            "   - Enable/disable strategies",
            "   - Set risk parameters",
            "   - Configure trading hours",
            "",
            "3. Test the setup:",
            "   python main.py --test",
            "",
            "4. Start paper trading:",
            "   python main.py",
            "",
            "5. Monitor logs:",
            "   tail -f logs/trading_bot.log",
            "",
            "6. Access dashboard (when implemented):",
            "   http://localhost:8000/dashboard"
        ]
        
        for step in steps:
            print(step)
        
        print("\n📚 Documentation:")
        print("   - README.md - Complete documentation")
        print("   - docs/ - Additional guides")
        print("   - GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot")
        
        print("\n⚠️  IMPORTANT REMINDERS:")
        print("   - Start with paper trading enabled")
        print("   - Never share your API credentials")
        print("   - Monitor your positions regularly")
        print("   - Understand the risks involved")


def main():
    """Main setup function."""
    parser = argparse.ArgumentParser(
        description="Setup Schwab Telegram Trading Bot"
    )
    parser.add_argument(
        '--skip-deps',
        action='store_true',
        help='Skip dependency installation'
    )
    parser.add_argument(
        '--dev',
        action='store_true',
        help='Install development dependencies'
    )
    
    args = parser.parse_args()
    
    setup = TradingBotSetup()
    setup.run_setup(skip_deps=args.skip_deps, dev_mode=args.dev)


if __name__ == '__main__':
    main()
