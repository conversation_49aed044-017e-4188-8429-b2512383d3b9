"""
Schwab API Client
Handles OAuth 2.0 authentication, rate limiting, and all API interactions.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import asyncio
import aiohttp
import json
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from urllib.parse import urlencode, parse_qs, urlparse
import structlog
from tenacity import retry, stop_after_attempt, wait_exponential

from .utils.rate_limiter import RateLimiter

logger = structlog.get_logger(__name__)


class SchwabClient:
    """
    Schwab API client with OAuth 2.0 authentication, automatic token refresh,
    and comprehensive rate limiting.
    """
    
    def __init__(self, client_id: str, client_secret: str, redirect_uri: str, account_number: str):
        """Initialize Schwab API client."""
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri
        self.account_number = account_number
        
        # API endpoints
        self.base_url = "https://api.schwabapi.com"
        self.auth_url = "https://api.schwabapi.com/oauth/authorize"
        self.token_url = "https://api.schwabapi.com/oauth/token"
        
        # Authentication tokens
        self.access_token: Optional[str] = None
        self.refresh_token: Optional[str] = None
        self.token_expires_at: Optional[datetime] = None
        
        # HTTP session
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Rate limiter (120 requests per minute)
        self.rate_limiter = RateLimiter(max_calls=120, time_window=60)
        
        # Request statistics
        self.request_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'rate_limited_requests': 0
        }
    
    async def initialize(self) -> None:
        """Initialize the HTTP session and authenticate."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'SchwabTradingBot/1.0.0',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        )
        
        # Try to load existing tokens
        await self._load_tokens()
        
        # Authenticate if no valid tokens
        if not self.access_token or await self._is_token_expired():
            await self.authenticate()
        
        logger.info("✅ Schwab client initialized successfully")
    
    async def authenticate(self) -> None:
        """Perform OAuth 2.0 authentication flow."""
        try:
            logger.info("🔐 Starting Schwab OAuth 2.0 authentication...")
            
            # Generate authorization URL
            auth_params = {
                'client_id': self.client_id,
                'redirect_uri': self.redirect_uri,
                'response_type': 'code',
                'scope': 'read write'
            }
            
            auth_url = f"{self.auth_url}?{urlencode(auth_params)}"
            
            logger.info(
                "🌐 Please visit the following URL to authorize the application:",
                url=auth_url
            )
            
            # In production, you would implement a web server to handle the callback
            # For now, we'll assume the authorization code is provided manually
            auth_code = input("Enter the authorization code from the callback URL: ")
            
            # Exchange authorization code for tokens
            await self._exchange_code_for_tokens(auth_code)
            
            logger.info("✅ Authentication completed successfully")
            
        except Exception as e:
            logger.error("❌ Authentication failed", error=str(e), exc_info=True)
            raise
    
    async def _exchange_code_for_tokens(self, auth_code: str) -> None:
        """Exchange authorization code for access and refresh tokens."""
        token_data = {
            'grant_type': 'authorization_code',
            'code': auth_code,
            'redirect_uri': self.redirect_uri
        }
        
        # Create basic auth header
        credentials = f"{self.client_id}:{self.client_secret}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        
        headers = {
            'Authorization': f'Basic {encoded_credentials}',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        async with self.session.post(
            self.token_url,
            data=urlencode(token_data),
            headers=headers
        ) as response:
            if response.status == 200:
                token_response = await response.json()
                
                self.access_token = token_response['access_token']
                self.refresh_token = token_response['refresh_token']
                
                # Calculate token expiration
                expires_in = token_response.get('expires_in', 3600)
                self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                
                # Save tokens for future use
                await self._save_tokens()
                
                logger.info("✅ Tokens obtained successfully")
            else:
                error_text = await response.text()
                logger.error("❌ Token exchange failed", status=response.status, error=error_text)
                raise Exception(f"Token exchange failed: {error_text}")
    
    async def _refresh_access_token(self) -> None:
        """Refresh the access token using the refresh token."""
        if not self.refresh_token:
            logger.error("❌ No refresh token available")
            await self.authenticate()
            return
        
        token_data = {
            'grant_type': 'refresh_token',
            'refresh_token': self.refresh_token
        }
        
        credentials = f"{self.client_id}:{self.client_secret}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        
        headers = {
            'Authorization': f'Basic {encoded_credentials}',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        try:
            async with self.session.post(
                self.token_url,
                data=urlencode(token_data),
                headers=headers
            ) as response:
                if response.status == 200:
                    token_response = await response.json()
                    
                    self.access_token = token_response['access_token']
                    if 'refresh_token' in token_response:
                        self.refresh_token = token_response['refresh_token']
                    
                    expires_in = token_response.get('expires_in', 3600)
                    self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                    
                    await self._save_tokens()
                    logger.info("✅ Access token refreshed successfully")
                else:
                    logger.error("❌ Token refresh failed", status=response.status)
                    await self.authenticate()
                    
        except Exception as e:
            logger.error("❌ Error refreshing token", error=str(e))
            await self.authenticate()
    
    async def _is_token_expired(self) -> bool:
        """Check if the access token is expired."""
        if not self.token_expires_at:
            return True
        
        # Add 5-minute buffer
        return datetime.now() >= (self.token_expires_at - timedelta(minutes=5))
    
    async def _ensure_valid_token(self) -> None:
        """Ensure we have a valid access token."""
        if not self.access_token or await self._is_token_expired():
            await self._refresh_access_token()
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict] = None,
        data: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Make authenticated API request with retry logic."""
        await self.rate_limiter.acquire()
        await self._ensure_valid_token()
        
        url = f"{self.base_url}{endpoint}"
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Accept': 'application/json'
        }
        
        if data:
            headers['Content-Type'] = 'application/json'
        
        self.request_stats['total_requests'] += 1
        
        try:
            async with self.session.request(
                method=method,
                url=url,
                params=params,
                json=data,
                headers=headers
            ) as response:
                
                if response.status == 429:  # Rate limited
                    self.request_stats['rate_limited_requests'] += 1
                    logger.warning("⚠️ Rate limited by Schwab API")
                    await asyncio.sleep(60)  # Wait 1 minute
                    raise Exception("Rate limited")
                
                if response.status >= 400:
                    error_text = await response.text()
                    self.request_stats['failed_requests'] += 1
                    logger.error(
                        "❌ API request failed",
                        method=method,
                        endpoint=endpoint,
                        status=response.status,
                        error=error_text
                    )
                    raise Exception(f"API request failed: {response.status} - {error_text}")
                
                self.request_stats['successful_requests'] += 1
                return await response.json()
                
        except Exception as e:
            logger.error("❌ Request error", method=method, endpoint=endpoint, error=str(e))
            raise
    
    async def get_account_info(self) -> Dict[str, Any]:
        """Get account information and balances."""
        endpoint = f"/trader/v1/accounts/{self.account_number}"
        return await self._make_request('GET', endpoint)
    
    async def get_positions(self) -> List[Dict[str, Any]]:
        """Get current positions."""
        endpoint = f"/trader/v1/accounts/{self.account_number}/positions"
        response = await self._make_request('GET', endpoint)
        return response.get('positions', [])
    
    async def get_quotes(self, symbols: List[str]) -> Dict[str, Any]:
        """Get real-time quotes for symbols."""
        symbols_str = ','.join(symbols)
        endpoint = f"/marketdata/v1/quotes"
        params = {'symbols': symbols_str}
        return await self._make_request('GET', endpoint, params=params)
    
    async def place_order(
        self,
        symbol: str,
        quantity: int,
        order_type: str,
        side: str,
        price: Optional[float] = None
    ) -> Dict[str, Any]:
        """Place a trading order."""
        order_data = {
            'orderType': order_type.upper(),
            'session': 'NORMAL',
            'duration': 'DAY',
            'orderStrategyType': 'SINGLE',
            'orderLegCollection': [{
                'instruction': side.upper(),
                'quantity': quantity,
                'instrument': {
                    'symbol': symbol,
                    'assetType': 'EQUITY'
                }
            }]
        }
        
        if price and order_type.upper() in ['LIMIT', 'STOP_LIMIT']:
            order_data['price'] = price
        
        endpoint = f"/trader/v1/accounts/{self.account_number}/orders"
        
        try:
            response = await self._make_request('POST', endpoint, data=order_data)
            return {
                'success': True,
                'order_id': response.get('orderId'),
                'response': response
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    async def get_orders(self, status: str = 'WORKING') -> List[Dict[str, Any]]:
        """Get orders by status."""
        endpoint = f"/trader/v1/accounts/{self.account_number}/orders"
        params = {'status': status}
        response = await self._make_request('GET', endpoint, params=params)
        return response.get('orders', [])
    
    async def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """Cancel a pending order."""
        endpoint = f"/trader/v1/accounts/{self.account_number}/orders/{order_id}"
        
        try:
            await self._make_request('DELETE', endpoint)
            return {'success': True}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _save_tokens(self) -> None:
        """Save tokens to file for persistence."""
        token_data = {
            'access_token': self.access_token,
            'refresh_token': self.refresh_token,
            'expires_at': self.token_expires_at.isoformat() if self.token_expires_at else None
        }
        
        try:
            with open('.schwab_tokens.json', 'w') as f:
                json.dump(token_data, f)
            logger.debug("💾 Tokens saved successfully")
        except Exception as e:
            logger.error("❌ Failed to save tokens", error=str(e))
    
    async def _load_tokens(self) -> None:
        """Load tokens from file."""
        try:
            with open('.schwab_tokens.json', 'r') as f:
                token_data = json.load(f)
            
            self.access_token = token_data.get('access_token')
            self.refresh_token = token_data.get('refresh_token')
            
            if token_data.get('expires_at'):
                self.token_expires_at = datetime.fromisoformat(token_data['expires_at'])
            
            logger.debug("📂 Tokens loaded successfully")
        except FileNotFoundError:
            logger.debug("📂 No existing tokens found")
        except Exception as e:
            logger.error("❌ Failed to load tokens", error=str(e))
    
    async def close(self) -> None:
        """Close the HTTP session."""
        if self.session:
            await self.session.close()
            logger.info("✅ Schwab client session closed")
    
    def get_request_stats(self) -> Dict[str, int]:
        """Get API request statistics."""
        return self.request_stats.copy()
